#!/usr/bin/env python3
"""
Claude AI 代码中文注释添加工具

为反混淆后的 Claude AI 集成代码添加详细的中文注释，
帮助理解代码的功能和实现细节。
"""

import re
import os
from pathlib import Path

class ChineseCommentAdder:
    def __init__(self):
        # 函数功能描述映射
        self.function_descriptions = {
            'callClaudeAPI': {
                'description': '调用 Claude API 的核心函数',
                'details': [
                    '处理系统提示词、用户提示词和助手提示词',
                    '支持提示词缓存以提高性能',
                    '支持温度参数控制响应随机性',
                    '支持信号中断机制',
                    '支持非交互式会话模式'
                ],
                'params': {
                    'systemPrompt': '系统提示词数组，定义AI的行为和角色',
                    'userPrompt': '用户输入的提示词',
                    'assistantPrompt': '助手的预设回复',
                    'enablePromptCaching': '是否启用提示词缓存',
                    'signal': '中断信号，用于取消请求',
                    'isNonInteractiveSession': '是否为非交互式会话',
                    'temperature': '温度参数，控制响应的随机性(0-1)'
                }
            },
            'streamClaudeAPI': {
                'description': '流式调用 Claude API 的函数',
                'details': [
                    '实现实时流式响应处理',
                    '集成工具调用功能',
                    '包含错误处理和重试逻辑',
                    '跟踪成本和性能指标',
                    '支持工具权限验证'
                ]
            },
            'mainConversationLoop': {
                'description': '主对话循环入口函数',
                'details': [
                    '过滤流事件和请求开始事件',
                    '委托给核心对话处理器',
                    '管理对话的整体流程'
                ]
            },
            'coreConversationHandler': {
                'description': '核心对话处理逻辑',
                'details': [
                    '处理消息压缩和上下文管理',
                    '管理对话状态和错误恢复',
                    '协调工具执行和结果集成',
                    '处理对话的递归调用'
                ]
            },
            'executeSingleTool': {
                'description': '执行单个工具的函数',
                'details': [
                    '检查工具可用性',
                    '验证输入参数',
                    '处理工具执行结果',
                    '处理取消和权限检查'
                ]
            },
            'executeToolWithValidation': {
                'description': '带验证的工具执行函数',
                'details': [
                    '全面的输入验证',
                    '权限检查',
                    '错误处理',
                    '执行指标跟踪',
                    '性能监控'
                ]
            },
            'formatErrorMessage': {
                'description': '格式化错误消息的函数',
                'details': [
                    '处理不同类型的错误',
                    '截断过长的消息',
                    '提供用户友好的错误描述'
                ]
            },
            'saveMemory': {
                'description': '保存用户记忆的函数',
                'details': [
                    '支持多种记忆类型(用户、本地、项目、托管)',
                    '使用AI智能更新记忆内容',
                    '基于文件的持久化存储'
                ]
            },
            'getModelTokenLimit': {
                'description': '获取模型Token限制的函数',
                'details': [
                    '根据模型类型返回相应的Token限制',
                    'Claude 3.5 和 Haiku 模型限制为 8192',
                    '其他模型默认限制为 20000'
                ]
            },
            'handleRefusalResponse': {
                'description': '处理拒绝响应的函数',
                'details': [
                    '检测API返回的拒绝状态',
                    '记录拒绝事件的指标',
                    '返回用户友好的拒绝消息'
                ]
            }
        }
        
        # 变量和常量描述
        self.variable_descriptions = {
            'ENABLE_PROMPT_CACHING': '启用提示词缓存的标志',
            'DEFAULT_TEMPERATURE': '默认的API温度参数',
            'REFUSAL_PREFIX': '拒绝消息的前缀',
            'CANCELLED_MESSAGE': '取消操作的消息',
            'CANCELLED_RESULT': '取消操作的结果',
            'CancellationError': '取消操作的错误类',
            'ProcessError': '进程错误类'
        }

    def add_function_comment(self, content, func_name, func_info):
        """为函数添加详细的中文注释"""
        # 构建详细的函数注释
        comment_lines = [
            f"/**",
            f" * {func_info['description']}",
            f" * "
        ]
        
        # 添加功能详情
        if 'details' in func_info:
            comment_lines.append(" * 功能详情:")
            for detail in func_info['details']:
                comment_lines.append(f" * - {detail}")
            comment_lines.append(" * ")
        
        # 添加参数说明
        if 'params' in func_info:
            comment_lines.append(" * 参数说明:")
            for param, desc in func_info['params'].items():
                comment_lines.append(f" * @param {{{param}}} {desc}")
            comment_lines.append(" * ")
        
        comment_lines.extend([
            " * @returns {Promise} 返回API调用的Promise对象",
            " */"
        ])
        
        comment_block = '\n'.join(comment_lines)
        
        # 查找函数定义并在前面添加注释
        pattern = rf'(async\s+function\*?\s+{re.escape(func_name)}\s*\()'
        replacement = f'{comment_block}\n\\1'
        content = re.sub(pattern, replacement, content, count=1)
        
        return content

    def add_inline_comments(self, content):
        """添加行内注释"""
        lines = content.split('\n')
        commented_lines = []
        
        for line in lines:
            original_line = line
            
            # 为常见的代码模式添加注释
            if 'yield {' in line and 'type: "stream_request_start"' in line:
                line += '  // 发出流请求开始事件'
            elif 'logMetric(' in line:
                line += '  // 记录性能指标'
            elif 'logError(' in line:
                line += '  // 记录错误信息'
            elif 'abortController.signal.aborted' in line:
                line += '  // 检查是否已取消操作'
            elif 'createMessage(' in line:
                line += '  // 创建消息对象'
            elif 'withRetry(' in line:
                line += '  // 使用重试机制执行'
            elif 'compressMessages(' in line:
                line += '  // 压缩消息历史'
            elif 'validateInput' in line:
                line += '  // 验证输入参数'
            elif 'getToolPermissionContext' in line:
                line += '  // 获取工具权限上下文'
            elif 'cache_control' in line:
                line += '  // 缓存控制配置'
            elif 'tool_use' in line and 'type:' in line:
                line += '  // 工具使用类型'
            elif 'tool_result' in line and 'type:' in line:
                line += '  // 工具执行结果'
            elif 'is_error: !0' in line or 'is_error: true' in line:
                line += '  // 标记为错误结果'
            
            commented_lines.append(line)
        
        return '\n'.join(commented_lines)

    def add_section_headers(self, content):
        """添加代码段落标题"""
        # 添加主要代码段的中文标题
        section_mappings = [
            ('// === CORE API FUNCTIONS ===', '// ==================== 核心 API 函数 ===================='),
            ('// === CONVERSATION MANAGEMENT ===', '// ==================== 对话管理 ===================='),
            ('// === TOOL EXECUTION ===', '// ==================== 工具执行 ===================='),
            ('// === MESSAGE FORMATTING ===', '// ==================== 消息格式化 ===================='),
            ('// === ERROR HANDLING ===', '// ==================== 错误处理 ===================='),
            ('// === MEMORY MANAGEMENT ===', '// ==================== 记忆管理 ===================='),
        ]
        
        for english, chinese in section_mappings:
            content = content.replace(english, chinese)
        
        return content

    def add_file_header(self, content, filename):
        """为文件添加头部注释"""
        file_descriptions = {
            'chunk_094.js': {
                'title': 'Claude API 核心调用模块',
                'description': '包含与 Claude API 直接通信的核心函数',
                'features': [
                    'Claude API 调用和流式处理',
                    '提示词缓存和性能优化',
                    '错误处理和重试逻辑',
                    '成本跟踪和指标收集',
                    '模型配置和限制管理'
                ]
            },
            'chunk_097.js': {
                'title': '对话管理和工具执行模块',
                'description': '管理对话流程和工具执行的核心逻辑',
                'features': [
                    '主对话循环管理',
                    '工具执行协调',
                    '消息处理和格式化',
                    '记忆管理集成',
                    '权限验证和安全控制'
                ]
            },
            'chunk_102.js': {
                'title': '会话管理模块',
                'description': '处理会话级别的功能和配置',
                'features': [
                    '会话配置管理',
                    '非交互式会话处理',
                    '用户偏好设置',
                    '会话状态维护'
                ]
            },
            'chunk_087.js': {
                'title': '认证和配置模块',
                'description': '管理认证和系统配置',
                'features': [
                    'OAuth 和 API 密钥处理',
                    '模型选择和配置',
                    '环境变量处理',
                    'API 端点管理'
                ]
            },
            'chunk_093.js': {
                'title': 'MCP 和外部工具集成模块',
                'description': '处理外部工具集成和模型上下文协议',
                'features': [
                    '模型上下文协议(MCP)支持',
                    '外部工具发现和管理',
                    '工具权限和安全控制',
                    '动态工具集成'
                ]
            }
        }
        
        file_info = file_descriptions.get(filename, {
            'title': 'Claude AI 集成模块',
            'description': 'Claude Code CLI 的核心功能模块',
            'features': ['核心功能实现']
        })
        
        header = f"""/**
 * ================================================================
 * {file_info['title']}
 * ================================================================
 * 
 * {file_info['description']}
 * 
 * 主要功能:
"""
        
        for feature in file_info['features']:
            header += f" * - {feature}\n"
        
        header += f""" * 
 * 文件: {filename}
 * 版本: Claude Code CLI v1.0.3
 * 
 * 注意: 此文件已从混淆代码反混淆并添加中文注释
 * ================================================================
 */

"""
        
        return header + content

    def process_file(self, input_path, output_path):
        """处理单个文件，添加中文注释"""
        print(f"正在为 {input_path} 添加中文注释...")
        
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        filename = os.path.basename(input_path)
        
        # 添加文件头部注释
        content = self.add_file_header(content, filename)
        
        # 为已知函数添加详细注释
        for func_name, func_info in self.function_descriptions.items():
            if func_name in content:
                content = self.add_function_comment(content, func_name, func_info)
        
        # 添加段落标题
        content = self.add_section_headers(content)
        
        # 添加行内注释
        content = self.add_inline_comments(content)
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已保存带中文注释的文件到 {output_path}")

    def process_directory(self):
        """处理整个目录"""
        input_dir = 'deobfuscated_claude_ai'
        output_dir = 'deobfuscated_claude_ai_commented'
        
        js_files = [
            'chunk_094.js',
            'chunk_097.js', 
            'chunk_102.js',
            'chunk_087.js',
            'chunk_093.js'
        ]
        
        for filename in js_files:
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)
            
            if os.path.exists(input_path):
                self.process_file(input_path, output_path)
            else:
                print(f"警告: 文件 {input_path} 不存在")

def main():
    """主函数"""
    print("Claude AI 代码中文注释添加工具")
    print("=" * 50)
    
    adder = ChineseCommentAdder()
    adder.process_directory()
    
    print("\n中文注释添加完成!")
    print("请查看 'deobfuscated_claude_ai_commented' 目录中的结果文件。")

if __name__ == "__main__":
    main()
