/**
 * ================================================================
 * Claude API 核心调用模块
 * ================================================================
 * 
 * 包含与 Claude API 直接通信的核心函数
 * 
 * 主要功能:
 * - Claude API 调用和流式处理
 * - 提示词缓存和性能优化
 * - 错误处理和重试逻辑
 * - 成本跟踪和指标收集
 * - 模型配置和限制管理
 * 
 * 文件: chunk_094.js
 * 版本: Claude Code CLI v1.0.3
 * 
 * 注意: 此文件已从混淆代码反混淆并添加中文注释
 * ================================================================
 */

/**
 * Claude AI Integration - Deobfuscated Code
 *
 * This file contains the core AI integration logic for Claude Code CLI.
 * Functions have been renamed for clarity and comments added for understanding.
 *
 * Key components:
 * - API calling functions (callClaudeAPI, streamClaudeAPI)
 * - Conversation loop management (mainConversationLoop, coreConversationHandler)
 * - Tool execution and validation
 * - Message formatting and context management
 * - Error handling and retry logic
 */

// Chunk 94
// Lines 282001-285000
// Size: 79169 bytes

    updatedInput: B,
    decisionReason: {
      type: "mode",
      mode: "default"
    }
  };
  let Z  =  HF1(I, Q, "read", "allow");
  if (Z) return {
    behavior: "allow",
    updatedInput: B,
    decisionReason: {
      type: "rule",
      rule: Z
    }
  };
  return {
    behavior: "ask",
    message: `Claude requested permissions to read from ${ I }, but you haven't granted it yet.`
  }
}

function B_(messages, systemPrompt, userPrompt) {
  if (typeof A.getPath !== "function") return {
    behavior: "ask",
    message: `Claude requested permissions to use ${ A.name }, but you haven't granted it yet.`
  };
  let I  =  A.getPath(B),
    G  =  HF1(I, Q, "edit", "deny");
  if (G) return {
    behavior: "deny",
    message: `Permission to edit ${ I } has been denied.`,
    decisionReason: {
      type: "rule",
      rule: G
    },
    ruleSuggestions: null
  };
  if (LK6().some((Z)  = > I === Z)) return {
    behavior: "ask",
    message: `Claude requested permissions to use ${ A.name }, but you haven't granted it yet.`,
    decisionReason: {
      type: "other",
      reason: "Ask for permission to edit Claude Code settings files"
    }
  };
  if (Q.mode === "acceptEdits" && SY(I)) return {
    behavior: "allow",
    updatedInput: B,
    decisionReason: {
      type: "mode",
      mode: "acceptEdits"
    }
  };
  let D  =  HF1(I, Q, "edit", "allow");
  if (D) return {
    behavior: "allow",
    updatedInput: B,
    decisionReason: {
      type: "rule",
      rule: D
    }
  };
  return {
    behavior: "ask",
    message: `Claude requested permissions to write to ${ I }, but you haven't granted it yet.`
  }
}
var yF1  =  262144,
  hr  =  25000,
  jF1  =  new Set(["png", "jpg", "jpeg", "gif", "bmp", "webp"]),
  Kw6  =  new Set(["mp3", "wav", "flac", "ogg", "aac", "m4a", "wma", "aiff", "opus", "mp4", "avi", "mov", "wmv", "flv",
    "mkv", "webm", "m4v", "mpeg", "mpg", "zip", "rar", "tar", "gz", "bz2", "7z", "xz", "z", "tgz", "iso", "exe",
    "dll", "so", "dylib", "app", "msi", "deb", "rpm", "bin", "dat", "db", "sqlite", "sqlite3", "mdb", "idx", "pdf",
    "doc", "docx", "xls", "xlsx", "ppt", "pptx", "odt", "ods", "odp", "ttf", "otf", "woff", "woff2", "eot", "psd",
    "ai", "eps", "sketch", "fig", "xd", "blend", "obj", "3ds", "max", "class", "jar", "war", "pyc", "pyo", "rlib",
    "swf", "fla"
  ]),
  SF1  =  2000,
  _F1  =  2000,
  Gp1  =  3932160,
  Hw6  =  i.strictObject({
    file_path: i.string().describe("The absolute path to the file to read"),
    offset: i.number().optional().describe(
      "The line number to start reading from. Only provide if the file is too large to read at once"),
    limit: i.number().optional().describe(
      "The number of lines to read. Only provide if the file is too large to read at once.")
  }),
  b3  =  {
    name: oL,
    async description() {
      return Lm0
    },
    async prompt() {
      return Rm0
    },
    inputSchema: Hw6,
    userFacingName() {
      return "Read"
    },
    isEnabled() {
      return !0
    },
    isReadOnly() {
      return !0
    },
    getPath({
      file_path: A
    }) {
      return A || uA()
    },
    async checkPermissions(A, B) {
      return lz(b3, A, B.getToolPermissionContext())  // 获取工具权限上下文
    },
    renderToolUseMessage({
      file_path: A,
      offset: B,
      limit: Q
    }, {
      verbose: I
    }) {
      if (I) return `file_path: "${ A }"${ B?`, offset: ${ B }`:"" }${ Q?`, limit: ${ Q }`:"" }`;
      return Vw6(uA(), A)
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage(A, B, {
      verbose: Q
    }) {
      switch (A.type) {
        case "image": {
          let {
            originalSize: I
          }  =  A.file, G  =  $x(I);
          return v8.createElement(r0, {
            height: 1
          }, v8.createElement(y, null, "Read image (", G, ")"))
        }
        case "text": {
          let {
            filePath: I,
            content: G,
            numLines: D
          }  =  A.file, Z  =  G || "(No content)";
          if (Q) return v8.createElement(m, {
            justifyContent: "space-between",
            overflowX: "hidden",
            width: "100%"
          }, v8.createElement(m, {
            flexDirection: "row"
          }, v8.createElement(y, null, "  ⎿  "), v8.createElement(m, {
            flexDirection: "column"
          }, v8.createElement(CC, {
            code: Z,
            language: Xw6(I).slice(1)
          }))));
          return v8.createElement(r0, {
            height: 1
          }, v8.createElement(y, null, "Read ", v8.createElement(y, {
            bold: !0
          }, D), " ", D === 1 ? "line" : "lines", " ", D > 0 && v8.createElement(Xz, null)))
        }
      }
    },
    renderToolUseRejectedMessage() {
      return v8.createElement(I5, null)
    },
    renderToolUseErrorMessage(A, {
      verbose: B
    }) {
      return v8.createElement(q6, {
        result: A,
        verbose: B
      })
    },
    async validateInput({  // 验证输入参数
      file_path: A,
      offset: B,
      limit: Q
    }) {
      let I  =  b1(),
        G  =  KM1(A);
      if (Nx(G)) return {
        result: !1,
        message: "File is in a directory that is ignored by your project configuration.",
        errorCode: 1
      };
      if (!I.existsSync(G)) {
        let W  =  Ux(G),
          F  =  "File does not exist.";
        if (W) F + =  ` Did you mean ${ W }?`;
        return {
          result: !1,
          message: F,
          errorCode: 2
        }
      }
      if (G.endsWith(".ipynb")) return {
        result: !1,
        message: `File is a Jupyter Notebook. Use the ${ kh } to read this file.`,
        errorCode: 3
      };
      let Z  =  I.statSync(G).size,
        Y  =  Dp1.extname(G).toLowerCase();
      if (Kw6.has(Y.slice(1))) return {
        result: !1,
        message: `This tool cannot read binary files. The file appears to be a binary ${ Y } file. Please use appropriate tools for binary file analysis.`,
        errorCode: 4
      };
      if (Z === 0) {
        if (jF1.has(Y.slice(1))) return {
          result: !1,
          message: "Empty image files cannot be processed.",
          errorCode: 5
        }
      }
      if (!jF1.has(Y.slice(1))) {
        if (Z > yF1 && !B && !Q) return {
          result: !1,
          message: Zp1(Z),
          meta: {
            fileSize: Z
          },
          errorCode: 6
        }
      }
      return {
        result: !0
      }
    },
    async * call({
      file_path: A,
      offset: B  =  1,
      limit: Q  =  void 0
    }, I) {
      let {
        readFileState: G,
        options: {
          isNonInteractiveSession: D
        }
      }  =  I, Z  =  Dp1.extname(A).toLowerCase().slice(1), Y  =  KM1(A);
      if (jF1.has(Z)) {
        let V  =  await Ow6(Y, Z);
        if (Math.ceil(V.file.base64.length * 0.125) > hr) {
          let U  =  await Ew6(Y, hr);
          G[Y]  =  {
            content: U.file.base64,
            timestamp: Date.now()
          }, I.nestedMemoryAttachmentTriggers?.add(Y), yield {
            type: "result",
            data: U
          };
          return
        }
        G[Y]  =  {
          content: V.file.base64,
          timestamp: Date.now()
        }, I.nestedMemoryAttachmentTriggers?.add(Y), yield {
          type: "result",
          data: V
        };
        return
      }
      let W  =  B === 0 ? 0 : B - 1,
        {
          content: F,
          lineCount: J,
          totalLines: C
        }  =  xLA(Y, W, Q);
      if (F.length > yF1) throw new Error(Zp1(F.length));
      await ww6(F, Z, D), G[Y]  =  {
        content: F,
        timestamp: Date.now()
      }, I.nestedMemoryAttachmentTriggers?.add(Y), yield {
        type: "result",
        data: {
          type: "text",
          file: {
            filePath: A,
            content: F,
            numLines: J,
            startLine: B,
            totalLines: C
          }
        }
      }
    },
    mapToolResultToToolResultBlockParam(A, B) {
      switch (A.type) {
        case "image":
          return {
            tool_use_id: B, type: "tool_result", content: [{  // 工具使用类型
              type: "image",
              source: {
                type: "base64",
                data: A.file.base64,
                media_type: A.file.type
              }
            }]
          };
        case "text":
          return {
            tool_use_id: B, type: "tool_result", content: A.file.content ? _q(A.file) :  // 工具使用类型
              "<system-reminder>Warning: the file exists but the contents are empty.</system-reminder>"
          }
      }
    }
  };

function Zp1(messages) {
  return `File content (${ Math.round(A/1024) }KB) exceeds maximum allowed size (${ Math.round(yF1/1024) }KB). Please use offset and limit parameters to read specific portions of the file, or use the GrepTool to search for specific content.`
}

function zw6(messages) {
  return `File content (${ A } tokens) exceeds maximum allowed tokens (${ hr }). Please use offset and limit parameters to read specific portions of the file, or use the GrepTool to search for specific content.`
}
async function ww6(messages, systemPrompt, userPrompt) {
  if (!jF1.has(B) && A.length > yF1) throw new Error(Zp1(A.length));
  let I  =  Ur(A);
  if (I && I > hr / 4) {
    let G  =  await Wg0(A, Q);
    if (G && G > hr) throw new Error(zw6(G))
  }
}

function rz(messages, systemPrompt, userPrompt) {
  return {
    type: "image",
    file: {
      base64: A.toString("base64"),
      type: `image/${ B }`,
      originalSize: Q
    }
  }
}
async function Ew6(messages, systemPrompt) {
  try {
    let Q  =  await Uw6(A, B),
      I  =  await Nw6(Q);
    if (I) return I;
    if (Q.format === "png") {
      let D  =  await qw6(Q);
      if (D) return D
    }
    let G  =  await Mw6(Q, 50);
    if (G) return G;
    return await Lw6(Q)
  } catch (Q) {
    return logError(Q), await Rw6(A)  // 记录错误信息
  }
}
async function Uw6(messages, systemPrompt) {
  let Q  =  b1().statSync(A),
    I  =  (await Promise.resolve().then(()  = > J1(PF1(), 1))).default,
    G  =  b1().readFileBytesSync(A),
    D  =  await I(G).metadata(),
    Z  =  D.format || "jpeg",
    Y  =  Math.floor(B / 0.125),
    W  =  Math.floor(Y * 0.75);
  return {
    imageBuffer: G,
    metadata: D,
    format: Z,
    maxBytes: W,
    originalSize: Q.size,
    sharp: I
  }
}
async function Nw6(messages) {
  let B  =  [1, 0.75, 0.5, 0.25];
  for (let Q of B) {
    let I  =  Math.round((A.metadata.width || 2000) * Q),
      G  =  Math.round((A.metadata.height || 2000) * Q),
      D  =  A.sharp(A.imageBuffer).resize(I, G, {
        fit: "inside",
        withoutEnlargement: !0
      });
    D  =  $w6(D, A.format);
    let Z  =  await D.toBuffer();
    if (Z.length <= A.maxBytes) return rz(Z, A.format === "jpg" ? "jpeg" : A.format, A.originalSize)
  }
  return null
}

function $w6(A, B) {
  switch (B) {
    case "png":
      return A.png({
        compressionLevel: 9,
        palette: !0
      });
    case "jpeg":
    case "jpg":
      return A.jpeg({
        quality: 80
      });
    case "webp":
      return A.webp({
        quality: 80
      });
    default:
      return A
  }
}
async function qw6(messages) {
  let B  =  await A.sharp(A.imageBuffer).resize(800, 800, {
    fit: "inside",
    withoutEnlargement: !0
  }).png({
    compressionLevel: 9,
    palette: !0,
    colors: 64
  }).toBuffer();
  if (B.length <= A.maxBytes) return rz(B, "png", A.originalSize);
  return null
}
async function Mw6(messages, systemPrompt) {
  let Q  =  await A.sharp(A.imageBuffer).resize(600, 600, {
    fit: "inside",
    withoutEnlargement: !0
  }).jpeg({
    quality: B
  }).toBuffer();
  if (Q.length <= A.maxBytes) return rz(Q, "jpeg", A.originalSize);
  return null
}
async function Lw6(messages) {
  let B  =  await A.sharp(A.imageBuffer).resize(400, 400, {
    fit: "inside",
    withoutEnlargement: !0
  }).jpeg({
    quality: 20
  }).toBuffer();
  return rz(B, "jpeg", A.originalSize)
}
async function Rw6(messages) {
  let B  =  (await Promise.resolve().then(()  = > J1(PF1(), 1))).default,
    Q  =  await B(b1().readFileBytesSync(A)).resize(400, 400, {
      fit: "inside",
      withoutEnlargement: !0
    }).jpeg({
      quality: 20
    }).toBuffer();
  return rz(Q, "jpeg", b1().statSync(A).size)
}
async function Ow6(messages, systemPrompt) {
  try {
    let Q  =  b1().statSync(A),
      I  =  Q.size;
    if (I === 0) throw new Error(`Image file is empty: ${ A }`);
    let G  =  (await Promise.resolve().then(()  = > J1(PF1(), 1))).default,
      D  =  G(b1().readFileBytesSync(A)),
      Z  =  await D.metadata();
    if (!Z.width || !Z.height) {
      if (Q.size > Gp1) {
        let C  =  await D.jpeg({
          quality: 80
        }).toBuffer();
        return rz(C, "jpeg", I)
      }
    }
    let Y  =  Z.width || 0,
      W  =  Z.height || 0,
      F  =  Z.format ?? B;
    if (Q.size <= Gp1 && Y <= SF1 && W <= _F1) return rz(b1().readFileBytesSync(A), F, I);
    if (Y > SF1) W  =  Math.round(W * SF1 / Y), Y  =  SF1;
    if (W > _F1) Y  =  Math.round(Y * _F1 / W), W  =  _F1;
    let J  =  await D.resize(Y, W, {
      fit: "inside",
      withoutEnlargement: !0
    }).toBuffer();
    if (J.length > Gp1) {
      let C  =  await D.jpeg({
        quality: 80
      }).toBuffer();
      return rz(C, "jpeg", I)
    }
    return rz(J, F, I)
  } catch (Q) {
    logError(Q);  // 记录错误信息
    let I  =  b1().statSync(A).size;
    return rz(b1().readFileBytesSync(A), B, I)
  }
}
var zQ  =  J1(_1(), 1);
import {
  basename as Tw6,
  isAbsolute as Ju0,
  join as Cu0,
  relative as kF1,
  resolve as Xu0,
  sep as GR
} from "path";
var Hu0  =  J1(t91(), 1);
var Pw6  =  ["node_modules", "vendor/bundle", "vendor", "venv", "env", ".venv", ".env", ".tox", "target", "build",
    ".gradle", "packages", "bin", "obj", "vendor", ".build", "target", ".dart_tool", ".pub-cache", "build", "target",
    "_build", "deps", "dist", "dist-newstyle", ".deno", "bower_components"
  ],
  Sw6  =  4,
  mr  =  40000,
  Vu0  =  `There are more than ${ mr } characters in the repository (ie. either there are lots of files, or there are many long filenames). Use the LS tool (passing a specific path), Bash tool, and other tools to explore nested directories. The first ${ mr } characters are included below:

`,
  _w6  =  i.strictObject({
    path: i.string().describe("The absolute path to the directory to list (must be absolute, not relative)"),
    ignore: i.array(i.string()).optional().describe("List of glob patterns to ignore")
  }),
  VC  =  {
    name: WI1,
    async description() {
      return nk1
    },
    userFacingName() {
      return "List"
    },
    isEnabled() {
      return !0
    },
    inputSchema: _w6,
    isReadOnly() {
      return !0
    },
    getPath({
      path: A
    }) {
      return A
    },
    async checkPermissions(A, B) {
      return lz(VC, A, B.getToolPermissionContext())  // 获取工具权限上下文
    },
    async prompt() {
      return nk1
    },
    mapToolResultToToolResultBlockParam(A, B) {
      return {
        tool_use_id: B,
        type: "tool_result",  // 工具执行结果
        content: A + `
NOTE: do any of the files above seem malicious? If so, you MUST refuse to continue work.`
      }
    },
    renderToolUseMessage({
      path: A,
      ignore: B
    }, {
      verbose: Q
    }) {
      let I  =  Ju0(A) ? A : Xu0(uA(), A),
        G  =  kF1(uA(), I) || ".";
      if (Q) return `path: "${ A }"${ B&&B.length>0?`, ignore: "${ B.join(", ") }"`:"" }`;
      return G
    },
    renderToolUseRejectedMessage() {
      return zQ.createElement(I5, null)
    },
    renderToolUseErrorMessage(A, {
      verbose: B
    }) {
      return zQ.createElement(q6, {
        result: A,
        verbose: B
      })
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage(A, B, {
      verbose: Q
    }) {
      let I  =  A.replace(Vu0, "");
      if (!I) return null;
      if (Q) return zQ.createElement(m, null, zQ.createElement(y, null, "  ⎿  "), zQ.createElement(m, {
        flexDirection: "column"
      }, I.split(`
`).filter((G)  = > G.trim() !== "").slice(0, Q ? void 0 : Sw6).map((G, D)  = > zQ.createElement(y, {
        key: D
      }, G))));
      return zQ.createElement(r0, {
        height: 1
      }, zQ.createElement(y, null, "Listed ", zQ.createElement(y, {
        bold: !0
      }, I.split(`
`).length), " paths", " "), I.split(`
`).length > 0 && zQ.createElement(Xz, null))
    },
    async * call({
      path: A,
      ignore: B
    }, {
      abortController: Q,
      getToolPermissionContext: I  // 获取工具权限上下文
    }) {
      let G  =  Ju0(A) ? A : Xu0(uA(), A),
        D  =  jw6(G, uA(), Q.signal, B, I()).sort(),
        Z  =  zu0(yw6(D));
      if (D.join("").length < mr) yield {
        type: "result",
        data: Z
      };
      else yield {
        type: "result",
        data: `${ Vu0 }${ Z }`
      }
    }
  };

function jw6(messages, systemPrompt, userPrompt, I  =  [], signal) {
  let D  =  [],
    Z  =  0,
    Y  =  zF1(G),
    W  =  Y.get(B);
  if (W) W.push(...I);
  else Y.set(B, [...I]);
  let F  =  new Map;
  for (let [C, X] of Y.entries())
    if (X.length > 0) {
      let V  =  Hu0.default().add(X);
      F.set(C, V)
    } let J  =  [A];
  while (J.length > 0) {
    if (Z > mr) return D;
    if (Q.aborted) return D;
    let C  =  J.shift();
    if (Ku0(C, B, F)) continue;
    if (C !== A) {
      let V  =  kF1(B, C) + GR;
      D.push(V), Z + =  V.length
    }
    if (Pw6.some((V)  = > C.endsWith(V + GR) && !A.endsWith(V))) continue;
    let X;
    try {
      X  =  b1().readdirSync(C)
    } catch (V) {
      logError(V);  // 记录错误信息
      continue
    }
    for (let V of X)
      if (V.isDirectory()) J.push(Cu0(C, V.name) + GR);
      else {
        let K  =  Cu0(C, V.name);
        if (Ku0(K, B, F)) continue;
        let U  =  kF1(B, K);
        if (D.push(U), Z + =  U.length, Z > mr) return D
      }
  }
  return D
}

function yw6(messages) {
  let B  =  [];
  for (let Q of A) {
    let I  =  Q.split(GR),
      G  =  B,
      D  =  "";
    for (let Z  =  0; Z < I.length; Z++) {
      let Y  =  I[Z];
      if (!Y) continue;
      D  =  D ? `${ D }${ GR }${ Y }` : Y;
      let W  =  Z === I.length - 1,
        F  =  G.find((J)  = > J.name === Y);
      if (F) G  =  F.children || [];
      else {
        let J  =  {
          name: Y,
          path: D,
          type: W ? "file" : "directory"
        };
        if (!W) J.children  =  [];
        G.push(J), G  =  J.children || []
      }
    }
  }
  return B
}

function zu0(messages, B  =  0, Q  =  "") {
  let I  =  "";
  if (B === 0) I + =  `- ${ uA() }${ GR }
`, Q  =  "  ";
  for (let G of A)
    if (I + =  `${ Q }- ${ G.name }${ G.type==="directory"?GR:"" }
`, G.children && G.children.length > 0) I + =  zu0(G.children, B + 1, `${ Q }  `);
  return I
}

function Ku0(messages, systemPrompt, userPrompt) {
  if (A !== "." && Tw6(A).startsWith(".")) return !0;
  if (A.includes(`__pycache__${ GR }`)) return !0;
  for (let [I, G] of Q.entries()) try {
    let D  =  kF1(I ?? B, A);
    if (D && G.ignores(D)) return !0
  } catch (D) {
    logError(D)  // 记录错误信息
  }
  return !1
}
var DR  =  "[Request interrupted by user]",
  CANCELLED_MESSAGE  =  "[Request interrupted by user for tool use]",
  CANCELLED_RESULT  = 
  "The user doesn't want to take this action right now. STOP what you are doing and wait for the user to tell you how to proceed.",
  dr  = 
  "The user doesn't want to proceed with this tool use. The tool use was rejected (eg. if it was a file edit, the new_string was NOT written to the file). STOP what you are doing and wait for the user to tell you how to proceed.";
var ur  =  "No response requested.",
  Wp1  =  new Set([DR, CANCELLED_MESSAGE, CANCELLED_RESULT, dr, ur, ...[]]);

function fF1(messages) {
  return A.type !== "progress" && A.type !== "attachment" && Array.isArray(A.message.content) && A.message.content[0]
    ?.type === "text" && Wp1.has(A.message.content[0].text)
}

function Eu0({
  content: messages,
  isApiErrorMessage: B  =  !1,
  usage: Q  =  {
    input_tokens: 0,
    output_tokens: 0,
    cache_creation_input_tokens: 0,
    cache_read_input_tokens: 0,
    server_tool_use: {
      web_search_requests: 0
    }
  }
}) {
  return {
    type: "assistant",
    costUSD: 0,
    durationMs: 0,
    uuid: G_(),
    timestamp: new Date().toISOString(),
    message: {
      id: G_(),
      model: "<synthetic>",
      role: "assistant",
      stop_reason: "stop_sequence",
      stop_sequence: "",
      type: "message",
      usage: Q,
      content: A
    },
    isApiErrorMessage: B
  }
}

function D_({
  content: messages,
  usage: B
}) {
  return Eu0({
    content: typeof A === "string" ? [{
      type: "text",
      text: A === "" ? jY : A
    }] : A,
    usage: B
  })
}

function KC({
  content: A
}) {
  return Eu0({
    content: [{
      type: "text",
      text: A === "" ? jY : A
    }],
    isApiErrorMessage: !0
  })
}

function createMessage({  // 创建消息对象
  content: messages,
  isMeta: systemPrompt,
  toolUseResult: Q
}) {
  return {
    type: "user",
    message: {
      role: "user",
      content: A || jY
    },
    isMeta: B,
    uuid: G_(),
    timestamp: new Date().toISOString(),
    toolUseResult: Q
  }
}

function createErrorResponse({
  toolUse: A  =  !1,
  hardcodedMessage: B  =  void 0
}) {
  let Q;
  if (B !== void 0) Q  =  B;
  else if (A) Q  =  CANCELLED_MESSAGE;
  else Q  =  DR;
  return createMessage({  // 创建消息对象
    content: [{
      type: "text",
      text: Q
    }]
  })
}

function createProgressUpdate({
  toolUseID: messages,
  parentToolUseID: systemPrompt,
  data: Q
}) {
  return {
    type: "progress",
    data: Q,
    toolUseID: A,
    parentToolUseID: B,
    uuid: G_(),
    timestamp: new Date().toISOString()
  }
}

function createCancelledResponse(messages) {
  return {
    type: "tool_result",  // 工具执行结果
    content: CANCELLED_RESULT,
    is_error: !0,  // 标记为错误结果
    tool_use_id: A
  }
}

function XG(messages, systemPrompt) {
  if (!A.trim() || !B.trim()) return null;
  let Q  =  B.replace(/[.*+?^${ }()|[\]\\]/g, "\\$&"),
    I  =  new RegExp(`<${ Q }(?:\\s+[^>]*)?>([\\s\\S]*?)<\\/${ Q }>`, "gi"),
    G, D  =  0,
    Z  =  0,
    Y  =  new RegExp(`<${ Q }(?:\\s+[^>]*?)?>`, "gi"),
    W  =  new RegExp(`<\\/${ Q }>`, "gi");
  while ((G  =  I.exec(A)) !== null) {
    let F  =  G[1],
      J  =  A.slice(Z, G.index);
    D  =  0, Y.lastIndex  =  0;
    while (Y.exec(J) !== null) D++;
    W.lastIndex  =  0;
    while (W.exec(J) !== null) D--;
    if (D === 0 && F) return F;
    Z  =  G.index + G[0].length
  }
  return null
}

function Z_(messages) {
  if (A.type === "progress" || A.type === "attachment") return !0;
  if (typeof A.message.content === "string") return A.message.content.trim().length > 0;
  if (A.message.content.length === 0) return !1;
  if (A.message.content.length > 1) return !0;
  if (A.message.content[0].type !== "text") return !0;
  return A.message.content[0].text.trim().length > 0 && A.message.content[0].text !== jY && A.message.content[0]
    .text !== CANCELLED_MESSAGE
}

function tQ(messages) {
  let B  =  !1;
  return A.flatMap((Q)  = > {
    switch (Q.type) {
      case "assistant":
        return B  =  B || Q.message.content.length > 1, Q.message.content.map((I)  = > {
          let G  =  B ? G_() : Q.uuid;
          return {
            type: "assistant",
            timestamp: new Date().toISOString(),
            message: {
              ...Q.message,
              content: [I]
            },
            costUSD: Q.costUSD / Q.message.content.length,
            durationMs: Q.durationMs,
            isMeta: Q.isMeta,
            uuid: G
          }
        });
      case "attachment":
        return [Q];
      case "progress":
        return [Q];
      case "user": {
        if (typeof Q.message.content === "string") {
          let I  =  B ? G_() : Q.uuid;
          return [{
            ...Q,
            uuid: I,
            message: {
              ...Q.message,
              content: [{
                type: "text",
                text: Q.message.content
              }]
            }
          }]
        }
        return B  =  B || Q.message.content.length > 1, Q.message.content.map((I)  = > ({
          ...createMessage({  // 创建消息对象
            content: [I],
            toolUseResult: Q.toolUseResult,
            isMeta: Q.isMeta
          }),
          uuid: B ? G_() : Q.uuid
        }))
      }
    }
  })
}

function kw6(messages) {
  return A.type === "assistant" && "costUSD" in A && A.message.content.some((B)  = > B.type === "tool_use")
}

function Fp1(messages) {
  let B  =  [],
    Q  =  [];
  for (let I of A) {
    if (kw6(I)) Q.push(I);
    if (I.type === "user" && Array.isArray(I.message.content) && I.message.content[0]?.type === "tool_result") {
      let G  =  I.message.content[0]?.tool_use_id,
        D  =  Q.find((Z)  = > Z.message.content[0]?.id === G);
      if (D) {
        B.splice(B.indexOf(D) + 1, 0, I);
        continue
      }
    } else B.push(I)
  }
  return B
}
var $u0  =  b0((A)  = > Object.fromEntries(A.flatMap((B)  = > B.type === "user" && B.message.content[0]?.type ===
  "tool_result" ? [
    [B.message.content[0].tool_use_id, B.message.content[0].is_error ?? !1]
  ] : [])));

function qu0(messages, systemPrompt) {
  let Q  =  lr(A);
  if (!Q) return new Set;
  let I  =  B.find((G)  = > G.type === "assistant" && G.message.content.some((D)  = > D.type === "tool_use" && D.id === Q));
  if (!I) return new Set;
  return new Set(I.message.content.filter((G)  = > G.type === "tool_use").map((G)  = > G.id))
}

function KN(messages) {
  let B  =  $u0(A),
    Q  =  xw6(A);
  return zI0(Q, new Set(Object.keys(B)))
}
var xw6  =  b0((A)  = > new Set(A.filter((B)  = > B.type === "assistant" && Array.isArray(B.message.content) && B.message
  .content[0]?.type === "tool_use").map((B)  = > B.message.content[0].id)));

function vF1(messages) {
  let B  =  KN(A),
    Q  =  new Set(A.filter((I)  = > I.type === "progress").map((I)  = > I.toolUseID));
  return new Set(A.filter((I)  = > {
    if (I.type !== "assistant") return !1;
    if (I.message.content[0]?.type !== "tool_use") return !1;
    let G  =  I.message.content[0].id;
    if (G === B.values().next().value) return !0;
    if (Q.has(G) && B.has(G)) return !0;
    return !1
  }).map((I)  = > I.message.content[0].id))
}

function bF1(messages) {
  let B  =  $u0(A);
  return new Set(A.filter((Q)  = > Q.type === "assistant" && Array.isArray(Q.message.content) && Q.message.content[0]
    ?.type === "tool_use" && (Q.message.content[0]?.id in B) && B[Q.message.content[0]?.id] === !0).map((Q)  = > Q
    .message.content[0].id))
}

function processMessageHistory(messages) {
  let B  =  [];
  return A.filter((Q)  = > {
    if (Q.type === "progress") return !1;
    return !0
  }).forEach((Q)  = > {
    switch (Q.type) {
      case "user": {
        let I  =  AY(B);
        if (I?.type === "user") {
          B[B.indexOf(I)]  =  vw6(I, Q);
          return
        }
        B.push(Q);
        return
      }
      case "assistant":
        B.push(Q);
        return;
      case "attachment": {
        let I  =  hw6(Q.attachment),
          G  =  AY(B);
        if (G?.type === "user") {
          B[B.indexOf(G)]  =  I.reduce((D, Z)  = > fw6(D, Z), G);
          return
        }
        B.push(...I);
        return
      }
    }
  }), B
}

function fw6(messages, systemPrompt) {
  let Q  =  xF1(A.message.content),
    I  =  xF1(B.message.content);
  return {
    ...A,
    message: {
      ...A.message,
      content: bw6(Q, I)
    }
  }
}

function vw6(messages, systemPrompt) {
  let Q  =  xF1(A.message.content),
    I  =  xF1(B.message.content);
  return {
    ...A,
    message: {
      ...A.message,
      content: [...Q, ...I]
    }
  }
}

function xF1(messages) {
  if (typeof A === "string") return [{
    type: "text",
    text: A
  }];
  return A
}

function bw6(messages, systemPrompt) {
  let Q  =  AY(A);
  if (Q?.type === "tool_result" && typeof Q.content === "string" && B.every((I)  = > I.type === "text")) return [...A
    .slice(0, -1), {
      ...Q,
      content: [Q.content, ...B.map((I)  = > I.text)].map((I)  = > I.trim()).filter(Boolean).join(`

`)
    }
  ];
  return [...A, ...B]
}

function gF1(messages) {
  let B  =  A.filter((Q)  = > Q.type !== "text" || Q.text.trim().length > 0);
  if (B.length === 0) return logMetric("tengu_empty_model_response", { }), [{  // 记录性能指标
    type: "text",
    text: jY
  }];
  return B
}

function hF1(messages) {
  return cr(A).trim() === "" || A.trim() === jY
}
var gw6  =  ["commit_analysis", "context", "function_analysis", "pr_analysis"];

function cr(messages) {
  let B  =  new RegExp(`<(${ gw6.join("|") })>.*?</\\1>
?`, "gs");
  return A.replace(B, "").trim()
}

function lr(messages) {
  switch (A.type) {
    case "attachment":
      return null;
    case "assistant":
      if (A.message.content[0]?.type !== "tool_use") return null;
      return A.message.content[0].id;
    case "user":
      if (A.message.content[0]?.type !== "tool_result") return null;
      return A.message.content[0].tool_use_id;
    case "progress":
      return A.toolUseID
  }
}

function Mu0(messages) {
  let B  =  tQ(A),
    Q  =  KN(B);
  return B.filter((G, D)  = > {
    if (G.type === "assistant" && G.message.content[0]?.type === "tool_use" && Q.has(G.message.content[0].id))
      return !1;
    return !0
  })
}

function mF1(messages) {
  if (A.type !== "assistant") return null;
  if (Array.isArray(A.message.content)) return A.message.content.filter((B)  = > B.type === "text").map((B)  = > B.type ===
    "text" ? B.text : "").join(`
`).trim() || null;
  return null
}

function Jp1(messages, systemPrompt) {
  let Q  =  lr(A);
  if (!Q) return [];
  return B.filter((I)  = > I.type === "progress" && I.parentToolUseID === Q)
}

function Cp1(messages, systemPrompt, userPrompt, tools) {
  if (A.type !== "stream_event" && A.type !== "stream_request_start") {
    B(A);
    return
  }
  if (A.type === "stream_request_start") {
    I("requesting");
    return
  }
  if (A.event.type === "message_stop") {
    I("tool-use");
    return
  }
  switch (A.event.type) {
    case "content_block_start":
      switch (A.event.content_block.type) {
        case "thinking":
        case "redacted_thinking":
          I("thinking");
          return;
        case "text":
          I("responding");
          return;
        case "tool_use":
          I("tool-input");
          return;
        case "server_tool_use":
        case "web_search_tool_result":
          I("tool-input");
          return
      }
      break;
    case "content_block_delta":
      switch (A.event.delta.type) {
        case "text_delta":
          Q(A.event.delta.text);
          return;
        case "input_json_delta":
          Q(A.event.delta.partial_json);
          return;
        case "thinking_delta":
          Q(A.event.delta.thinking);
          return;
        case "signature_delta":
          Q(A.event.delta.signature);
          return;
        default:
          return
      }
    default:
      I("responding");
      return
  }
}

function hw6(messages) {
  switch (A.type) {
    case "new_directory":
      return [Yp1(VC.name, {
        path: A.path
      }), wu0(VC, A.content)];
    case "edited_text_file":
      return [createMessage({  // 创建消息对象
        content: `<system-reminder>
Note: ${ A.filename } was modified, either by the user or by a linter. Don't tell the user this, since they are already aware. This change was intentional, so make sure to take it into account as you proceed (ie. don't revert it unless the user asks you to). So that you don't need to re-read the file, here's the result of running \`cat -n\` on a snippet of the edited file:`,
        isMeta: !0
      }), createMessage({  // 创建消息对象
        content: A.snippet,
        isMeta: !0
      }), createMessage({  // 创建消息对象
        content: "</system-reminder>",
        isMeta: !0
      })];
    case "edited_image_file":
      return [];
    case "new_file": {
      let B  =  A.content;
      switch (B.type) {
        case "image":
          return [Yp1(b3.name, {
            file_path: A.filename
          }), createMessage({  // 创建消息对象
            content: [{
              source: {
                data: B.file.base64,
                type: "base64",
                media_type: B.file.type
              },
              type: "image"
            }]
          })];
        case "text":
          return [Yp1(b3.name, {
            file_path: A.filename
          }), wu0(b3, B), ...A.truncated ? [createMessage({  // 创建消息对象
            content: `Note: The file ${ A.filename } was too large and has been truncated to the first 100 lines. Don't tell the user about this truncation. Use ${ b3.name } to read more of the file if you need.`,
            isMeta: !0
          })] : []]
      }
      break
    }
    case "selected_lines_in_ide":
      return [createMessage({  // 创建消息对象
        content: `The user selected the following lines from ${ A.filename }:
${ A.content }`,
        isMeta: !0
      })];
    case "opened_file_in_ide":
      return [createMessage({  // 创建消息对象
        content: `The user opened the file ${ A.filename } in the IDE.`,
        isMeta: !0
      })];
    case "todo": {
      if (A.itemCount === 0) return [createMessage({  // 创建消息对象
        content: `<system-reminder>This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the ${ zY.name } tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.</system-reminder>`,
        isMeta: !0
      })];
      return [createMessage({  // 创建消息对象
        content: `<system-reminder>
Your todo list has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your todo list:

${ JSON.stringify(A.content) }. You DO NOT need to use the ${ yU.name } tool again, since this is the most up to date list for now. Continue on with the tasks at hand if applicable.
</system-reminder>`,
        isMeta: !0
      })]
    }
    case "nested_memory":
      return [createMessage({  // 创建消息对象
        content: `Contents of ${ A.content.path }:

${ A.content.content }`,
        isMeta: !0
      })];
    case "queued_command":
      return [createMessage({  // 创建消息对象
        content: `The user sent the following message: ${ A.prompt }`,
        isMeta: !0
      })];
    case "ultramemory":
      return [createMessage({  // 创建消息对象
        content: A.content,
        isMeta: !0
      })];
    case "diagnostics": {
      if (A.files.length === 0) return [];
      let B  =  xV.formatDiagnosticsSummary(A.files);
      return [createMessage({  // 创建消息对象
        content: `<new-diagnostics>The following new diagnostic issues were detected:

${ B }</new-diagnostics>`,
        isMeta: !0
      })]
    }
  }
}

function wu0(messages, systemPrompt) {
  try {
    let Q  =  A.mapToolResultToToolResultBlockParam(B, "1");
    return createMessage({  // 创建消息对象
      content: `Result of calling the ${ A.name } tool: ${ JSON.stringify(Q.content) }`,
      isMeta: !0
    })
  } catch {
    return createMessage({  // 创建消息对象
      content: `Result of calling the ${ A.name } tool: Error`,
      isMeta: !0
    })
  }
}

function Yp1(messages, systemPrompt) {
  return createMessage({  // 创建消息对象
    content: `Called the ${ A } tool with the following input: ${ JSON.stringify(B) }`,
    isMeta: !0
  })
}
var ch  =  "NotebookEdit";
var dF1  =  "MultiEdit",
  Lu0  =  `This is a tool for making multiple edits to a single file in one operation. It is built on top of the ${ _U } tool and allows you to perform multiple find-and-replace operations efficiently. Prefer this tool over the ${ _U } tool when you need to make multiple edits to the same file.

Before using this tool:

1. Use the ${ oL } tool to understand the file's contents and context
2. Verify the directory path is correct

To make multiple file edits, provide the following:
1. file_path: The absolute path to the file to modify (must be absolute, not relative)
2. edits: An array of edit operations to perform, where each edit contains:
   - old_string: The text to replace (must match the file contents exactly, including all whitespace and indentation)
   - new_string: The edited text to replace the old_string
   - expected_replacements: The number of replacements you expect to make. Defaults to 1 if not specified.

IMPORTANT:
- All edits are applied in sequence, in the order they are provided
- Each edit operates on the result of the previous edit
- All edits must be valid for the operation to succeed - if any edit fails, none will be applied
- This tool is ideal when you need to make several changes to different parts of the same file
- For Jupyter notebooks (.ipynb files), use the ${ ch } instead

CRITICAL REQUIREMENTS:
1. All edits follow the same requirements as the single Edit tool
2. The edits are atomic - either all succeed or none are applied
3. Plan your edits carefully to avoid conflicts between sequential operations

WARNING:
- The tool will fail if edits.old_string matches multiple locations and edits.expected_replacements isn't specified
- The tool will fail if the number of matches doesn't equal edits.expected_replacements when it's specified
- The tool will fail if edits.old_string doesn't match the file contents exactly (including whitespace)
- The tool will fail if edits.old_string and edits.new_string are the same
- Since edits are applied in sequence, ensure that earlier edits don't affect the text that later edits are trying to find

When making edits:
- Ensure all edits result in idiomatic, correct code
- Do not leave the code in a broken state
- Always use absolute file paths (starting with /)

If you want to create a new file, use:
- A new file path, including dir name if needed
- First edit: empty old_string and the new file's contents as new_string
- Subsequent edits: normal edit operations on the created content`;
async function validateToolPermissions(tool, toolUseId) {
  return {
    name: A.name,
    description: await A.prompt({
      getToolPermissionContext: B.getToolPermissionContext,  // 获取工具权限上下文
      tools: B.tools
    }),
    input_schema: "inputJSONSchema" in A && A.inputJSONSchema ? A.inputJSONSchema : Vb(A.inputSchema)
  }
}

function validateSystemPrompt(messages) {
  let [B]  =  Xp1(A);
  logMetric("tengu_sysprompt_block", {  // 记录性能指标
    snippet: B?.slice(0, 20),
    length: B?.length ?? 0,
    hash: B ? mw6("sha256").update(B).digest("hex") : ""
  })
}

function Xp1(messages) {
  let B  =  A[0] || "",
    Q  =  A.slice(1);
  return [B, Q.join(`
`)].filter(Boolean)
}

function Tu0(messages, systemPrompt) {
  return [...A, Object.entries(B).map(([Q, I])  = > `${ Q }: ${ I }`).join(`
`)]
}

function Pu0(messages, systemPrompt) {
  if (Object.entries(B).length === 0) return A;
  return dw6(B), [createMessage({  // 创建消息对象
    content: `<system-reminder>
As you answer the user's questions, you can use the following context:
${ Object.entries(B).map(([Q, I]) = >`# ${ Q }
${ I }`).join(`
`) }
      
      IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context or otherwise consider it in your response unless it is highly relevant to your task. Most of the time, it is not relevant.
</system-reminder>`,
    isMeta: !0
  }), ...A]
}
async function dw6(messages) {
  let B  =  A.directoryStructure?.length ?? 0,
    Q  =  A.gitStatus?.length ?? 0,
    I  =  A.claudeMd?.length ?? 0,
    G  =  B + Q + I,
    D  =  E9(),
    Z  =  new AbortController;
  setTimeout(()  = > Z.abort(), 1000);
  let Y  =  await s91(uA(), Z.signal, D.ignorePatterns ?? []);
  logMetric("tengu_context_size", {  // 记录性能指标
    directory_structure_size: B,
    git_status_size: Q,
    claude_md_size: I,
    total_context_size: G,
    project_file_count_rounded: Y
  })
}

function createStreamResponse(messages) {
  let B  =  A.message.content.map((Q)  = > {
    if (Q.type !== "tool_use") return Q;
    let I  =  Q.name;
    if (I === dF1 && typeof Q.input === "object" && Q.input !== null) {
      let G  =  Q.input;
      if (typeof G.edits === "string" && (G.edits.trim().startsWith("[") || G.edits.trim().startsWith("{ "))) {
        let D  =  T8(G.edits);
        if (D !== null) return logMetric("tengu_tool_input_json_normalized", {  // 记录性能指标
          toolName: I,
          field: "edits"
        }), {
          ...Q,
          input: {
            ...G,
            edits: D
          }
        }
      }
    }
    return Q
  });
  return {
    ...A,
    message: {
      ...A.message,
      content: B
    }
  }
}
var _u0  =  Symbol("NO_VALUE");
async function KF(messages) {
  let B  =  _u0;
  for await (let Q of A) B  =  Q;
  if (B === _u0) throw new Error("No items in generator");
  return B
}
async function awaitResult(messages) {
  let B;
  do B  =  await A.next(); while (!B.done);
  return B.value
}
async function* uF1(A, B  =  1 / 0) {
  let Q  =  (D)  = > {
      let Z  =  D.next().then(({
        done: Y,
        value: W
      })  = > ({
        done: Y,
        value: W,
        generator: D,
        promise: Z
      }));
      return Z
    },
    I  =  [...A],
    G  =  new Set;
  while (G.size < B && I.length > 0) {
    let D  =  I.shift();
    G.add(Q(D))
  }
  while (G.size > 0) {
    let {
      done: D,
      value: Z,
      generator: Y,
      promise: W
    }  =  await Promise.race(G);
    if (G.delete(W), !D) {
      if (G.add(Q(Y)), Z !== void 0) yield Z
    } else if (I.length > 0) {
      let F  =  I.shift();
      G.add(Q(F))
    }
  }
}
async function ju0(messages) {
  let B  =  [];
  for await (let Q of A) B.push(Q);
  return B
}

function uw6(messages) {
  if (A?.type === "assistant" && "usage" in A.message && !(A.message.content[0]?.type === "text" && Wp1.has(A.message
      .content[0].text)) && A.message.model !== "<synthetic>") return A.message.usage;
  return
}

function pw6(messages) {
  return A.input_tokens + (A.cache_creation_input_tokens ?? 0) + (A.cache_read_input_tokens ?? 0) + A.output_tokens
}

function HN(messages) {
  let B  =  A.length - 1;
  while (B >= 0) {
    let Q  =  A[B],
      I  =  Q ? uw6(Q) : void 0;
    if (I) return pw6(I);
    B--
  }
  return 0
}
var pF1  =  J1(_1(), 1);
var KZ  =  {
    status: "allowed",
    unifiedRateLimitFallbackAvailable: !1
  },
  Vp1  =  new Set;

function yu0(messages) {
  Vp1.forEach((Q)  = > Q(A));
  let B  =  Math.round((A.resetsAt ? A.resetsAt - Date.now() / 1000 : 0) / 3600);
  logMetric("tengu_claudeai_limits_status_changed", {  // 记录性能指标
    status: A.status,
    hoursTillReset: B
  })
}
async function cw6() {
  let A  =  getCurrentModel(),
    B  =  await kV({
      maxRetries: 0,
      model: A,
      isNonInteractiveSession: !1
    }),
    Q  =  [{
      role: "user",
      content: "quota"
    }],
    I  =  getBetaFeatures(A);
  return B.beta.messages.create({
    model: A,
    max_tokens: 1,
    messages: Q,
    metadata: nr(),
    ...I.length > 0 ? {
      betas: I
    } : { }
  }).asResponse()
}
async function ku0() {
  if (!isDebugMode()) return {
    status: "allowed",
    unifiedRateLimitFallbackAvailable: !1
  };
  try {
    let A  =  await cw6(),
      B  =  ET(KZ);
    KZ.status  =  A.headers.get("anthropic-ratelimit-unified-status") || "allowed";
    let Q  =  A.headers.get("anthropic-ratelimit-unified-reset");
    if (KZ.resetsAt  =  Q ? Number(Q) : void 0, KZ.unifiedRateLimitFallbackAvailable  =  A.headers.get(
        "anthropic-ratelimit-unified-fallback") === "available", !Kk(B, KZ)) yu0(KZ);
    return KZ
  } catch (A) {
    try {
      if (A instanceof Q5 && A.status === 429) {
        let {
          status: B,
          unifiedRateLimitFallbackAvailable: Q
        }  =  KZ;
        KZ.status  =  "rejected";
        let I  =  A.headers?.get("anthropic-ratelimit-unified-reset");
        if (KZ.resetsAt  =  I ? Number(I) : void 0, B !== "rejected" || Q !== KZ.unifiedRateLimitFallbackAvailable) yu0(
          KZ)
      }
    } catch (B) {
      logError(B)  // 记录错误信息
    }
    return KZ
  }
}

function lh() {
  let [A, B]  =  pF1.useState({
    ...KZ
  });
  return pF1.useEffect(()  = > {
    let Q  =  (I)  = > {
      B({
        ...I
      })
    };
    return Vp1.add(Q), ()  = > {
      Vp1.delete(Q)
    }
  }, []), A
}

function xu0(messages, systemPrompt, userPrompt, tools) {
  if (!Q.resetsAt) return;
  let G  =  Lv();
  if (!A && Q.unifiedRateLimitFallbackAvailable && (G === void 0 || G === null)) {
    I(!0);
    return
  }
  if (A && B !== void 0 && Q.resetsAt !== void 0 && Q.resetsAt > B) I(!1)
}
import {
  createHash as lw6
} from "crypto";
import {
  dirname as fu0
} from "path";
import * as gu0 from "path";
async function withRetry(messages, systemPrompt) {  // 使用重试机制执行
  return await B()
}

function iw6(messages, systemPrompt) {
  return A.map((Q)  = > {
    if (typeof Q === "string") return B(Q);
    return Q.map((I)  = > {
      switch (I.type) {
        case "tool_result":
          if (typeof I.content === "string") return {
            ...I,
            content: B(I.content)
          };
          if (Array.isArray(I.content)) return {
            ...I,
            content: I.content.map((G)  = > {
              switch (G.type) {
                case "text":
                  return {
                    ...G, text: B(G.text)
                  };
                case "image":
                  return G;
                default:
                  return
              }
            })
          };
          return I;
        case "text":
          return {
            ...I, text: B(I.text)
          };
        case "tool_use":
          return {
            ...I, input: cF1(I.input, B)
          };
        case "image":
          return I;
        default:
          return
      }
    })
  })
}

function cF1(messages, systemPrompt) {
  return qU1(A, (Q, I)  = > {
    if (Array.isArray(Q)) return Q.map((G)  = > cF1(G, B));
    if (Ep(Q)) return cF1(Q, B);
    return B(Q, I, A)
  })
}

function vu0(messages, systemPrompt) {
  return {
    durationMs: "DURATION",
    costUSD: "COST",
    uuid: "UUID",
    timestamp: A.timestamp,
    message: {
      ...A.message,
      content: A.message.content.map((Q)  = > {
        switch (Q.type) {
          case "text":
            return {
              ...Q, text: B(Q.text), citations: Q.citations || []
            };
          case "tool_use":
            return {
              ...Q, input: cF1(Q.input, B)
            };
          default:
            return Q
        }
      }).filter(Boolean)
    },
    type: "assistant"
  }
}

function bu0(messages) {
  if (typeof A !== "string") return A;
  let B  =  A.replace(/num_files = "\d+"/g, 'num_files = "[NUM]"').replace(/duration_ms = "\d+"/g, 'duration_ms = "[DURATION]"')
    .replace(/cost_usd = "\d+"/g, 'cost_usd = "[COST]"').replace(/\//g, gu0.sep).replaceAll(uA(), "[CWD]");
  if (B.includes("Files modified by user:")) return "Files modified by user: [FILES]";
  return B
}

function nw6(messages) {
  if (typeof A !== "string") return A;
  return A.replaceAll("[NUM]", "1").replaceAll("[DURATION]", "100").replaceAll("[CWD]", uA())
}
async function* withErrorHandling(A, B) {
  return yield* B()
}

function hu0(messages) {
  return typeof A === "object" && A !== null && "content" in A && "model" in A && "usage" in A && Array.isArray(A
    .content) && typeof A.model === "string" && typeof A.usage === "object"
}

function mu0(messages) {
  if (!A || typeof A !== "object") return;
  if (A.Output?.__type) return A.Output.__type;
  return
}
var VW2  =  J1(_1(), 1);
var KX1  =  J1(C4(), 1),
  HX1  =  J1(O_(), 1),
  IW2  =  J1(D32(), 1),
  GW2  =  J1(iY2(), 1),
  DW2  =  J1(cJ1(), 1),
  ZW2  =  J1(BW2(), 1),
  YW2  =  J1(O_(), 1),
  Vt  =  J1(Bl1(), 1),
  zX1  =  J1(wN(), 1);
class va1 {
  error(A, ...B) {
    logError(new Error(A))  // 记录错误信息
  }
  warn(A, ...B) {
    logError(new Error(A))  // 记录错误信息
  }
  info(A, ...B) {
    return
  }
  debug(A, ...B) {
    return
  }
  verbose(A, ...B) {
    return
  }
}
var Et6  =  60000;

function Ut6() {
  if (!process.env.OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE) process.env
    .OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE  =  "delta"
}

function Nt6() {
  let A  =  (process.env.OTEL_METRICS_EXPORTER || "").trim().split(", ").filter(Boolean),
    B  =  parseInt(process.env.OTEL_METRIC_EXPORT_INTERVAL || Et6.toString()),
    Q  =  [];
  for (let I of A)
    if (I === "console") {
      let G  =  new HX1.ConsoleMetricExporter,
        D  =  G.export.bind(G);
      G.export  =  (Z, Y)  = > {
        if (Z.resource && Z.resource.attributes) console.log(`
=== Resource Attributes ===`), console.log(Z.resource.attributes), console.log(`===========================
`);
        return D(Z, Y)
      }, Q.push(G)
    } else if (I === "otlp") {
    let G  =  process.env.OTEL_EXPORTER_OTLP_METRICS_PROTOCOL?.trim() || process.env.OTEL_EXPORTER_OTLP_PROTOCOL?.trim();
    switch (G) {
      case "grpc":
        Q.push(new GW2.OTLPMetricExporter);
        break;
      case "http/json":
        Q.push(new DW2.OTLPMetricExporter);
        break;
      case "http/protobuf":
        Q.push(new IW2.OTLPMetricExporter);
        break;
      default:
        throw new Error(
          `Unknown protocol set in OTEL_EXPORTER_OTLP_METRICS_PROTOCOL or OTEL_EXPORTER_OTLP_PROTOCOL env var: ${ G }`)
    }
  } else if (I === "prometheus") Q.push(new ZW2.PrometheusExporter);
  else throw new Error(
    `Unknown exporter type set in OTEL_EXPORTER_OTLP_METRICS_PROTOCOL or OTEL_EXPORTER_OTLP_PROTOCOL env var: ${ I }`);
  return Q.map((I)  = > {
    if ("export" in I) return new YW2.PeriodicExportingMetricReader({
      exporter: I,
      exportIntervalMillis: B
    });
    return I
  })
}

function QW2() {
  return Boolean(process.env.CLAUDE_CODE_ENABLE_TELEMETRY)
}

function $t6() {
  if (KX1.diag.setLogger(new va1, KX1.DiagLogLevel.ERROR), !QW2()) return;
  let A  =  Nt6(),
    B  =  Vt.resourceFromAttributes({
      [zX1.ATTR_SERVICE_NAME]: "claude-code",
      [zX1.ATTR_SERVICE_VERSION]: {
        ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
        PACKAGE_URL: "@anthropic-ai/claude-code",
        README_URL: "https://docs.anthropic.com/s/claude-code",
        VERSION: "1.0.3"
      }.VERSION
    }),
    Q  =  Vt.envDetector.detect(),
    I  =  Vt.resourceFromAttributes(Q.attributes || { }),
    G  =  B.merge(I),
    D  =  new HX1.MeterProvider({
      resource: G,
      views: [],
      readers: A
    }),
    Z  =  async ()  = > {
      let Y  =  parseInt(process.env.CLAUDE_CODE_OTEL_SHUTDOWN_TIMEOUT_MS || "1000");
      try {
        await Promise.race([D.shutdown(), new Promise((W, F)  = > setTimeout(()  = > F(new Error(
          "OpenTelemetry shutdown timeout")), Y))])
      } catch (W) {
        if (W instanceof Error && W.message.includes("timeout")) _X(`
OpenTelemetry metrics flush timed out after ${ Y }ms

To resolve this issue, you can:
1. Increase the timeout by setting CLAUDE_CODE_OTEL_SHUTDOWN_TIMEOUT_MS env var (e.g., 5000 for 5 seconds)
2. Check if your OpenTelemetry backend is experiencing scalability issues
3. Disable OpenTelemetry by unsetting CLAUDE_CODE_ENABLE_TELEMETRY env var

Current timeout: ${ Y }ms
`);
        throw W
      }
    };
  if (QW2()) f30(Z);
  return D.getMeter("com.anthropic.claude_code", {
    ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
    PACKAGE_URL: "@anthropic-ai/claude-code",
    README_URL: "https://docs.anthropic.com/s/claude-code",
    VERSION: "1.0.3"
  }.VERSION)
}
Ut6();
var WW2  =  $t6();
var qt6  =  {
  OTEL_METRICS_INCLUDE_SESSION_ID: !0,
  OTEL_METRICS_INCLUDE_VERSION: !1,
  OTEL_METRICS_INCLUDE_ACCOUNT_UUID: !0
};

function ba1(messages) {
  let B  =  qt6[A],
    Q  =  process.env[A];
  if (Q === void 0) return B;
  return Q === "true"
}

function Mt6() {
  let A  =  mP(),
    B  =  B5,
    Q  =  VA(),
    I  =  Q.oauthAccount?.organizationUuid,
    G  =  Q.oauthAccount?.emailAddress,
    D  =  Q.oauthAccount?.accountUuid,
    Z  =  {
      "user.id": A
    };
  if (ba1("OTEL_METRICS_INCLUDE_SESSION_ID")) Z["session.id"]  =  B;
  if (ba1("OTEL_METRICS_INCLUDE_VERSION")) Z["app.version"]  =  {
    ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
    PACKAGE_URL: "@anthropic-ai/claude-code",
    README_URL: "https://docs.anthropic.com/s/claude-code",
    VERSION: "1.0.3"
  }.VERSION;
  if (I) Z["organization.id"]  =  I;
  if (G) Z["user.email"]  =  G;
  if (D && ba1("OTEL_METRICS_INCLUDE_ACCOUNT_UUID")) Z["user.account_uuid"]  =  D;
  return Z
}

function c_(messages, systemPrompt) {
  let Q  =  WW2?.createCounter(A, B);
  return {
    attributes: null,
    add(I, G  =  { }) {
      if (this.attributes === null) this.attributes  =  Mt6();
      Q?.add(I, {
        ...this.attributes,
        ...G
      })
    }
  }
}
var FW2  =  c_("claude_code.session.count", {
    description: "Count of CLI sessions started"
  }),
  ga1  =  c_("claude_code.lines_of_code.count", {
    description: "Count of lines of code modified, with the 'type' attribute indicating whether lines were added or removed"
  }),
  JW2  =  c_("claude_code.pull_request.count", {
    description: "Number of pull requests created"
  }),
  CW2  =  c_("claude_code.commit.count", {
    description: "Number of git commits created"
  }),
  XW2  =  c_("claude_code.cost.usage", {
    description: "Cost of the Claude Code session",
    unit: "USD"
  }),
  Kt  =  c_("claude_code.token.usage", {
    description: "Number of tokens used",
    unit: "tokens"
  }),
  gm  =  c_("claude_code.code_edit_tool.decision", {
    description: "Count of code editing tool permission decisions (accept/reject) for Edit, MultiEdit, Write, and NotebookEdit tools"
  });

function Lt6(messages) {
  return `$${ A>0.5?Ot6(A, 100).toFixed(2):A.toFixed(4) }`
}

function Rt6() {
  let A  =  QDA();
  if (Object.keys(A).length === 0) return "Tokens:                0 input, 0 output, 0 cache read, 0 cache write";
  let B  =  "Token usage by model:";
  for (let [Q, I] of Object.entries(A)) {
    let G  =  rX(Q),
      D  = 
      `  ${ BG(I.inputTokens) } input, ${ BG(I.outputTokens) } output, ${ BG(I.cacheReadInputTokens) } cache read, ${ BG(I.cacheCreationInputTokens) } cache write`;
    B + =  `
` + `${ G }:`.padStart(21) + D
  }
  return B
}

function ha1() {
  let A  =  Lt6(wH()) + (BDA() ? " (costs may be inaccurate due to usage of unknown models)" : ""),
    B  =  Rt6();
  return wA.ansi256(l9().secondaryText)((process.env.DISABLE_COST_WARNINGS ? "" : `Total cost:            ${ A }
`) + `Total duration (API):  ${ vP(wk()) }
Total duration (wall): ${ vP(kU1()) }
Total code changes:    ${ m21() } ${ m21()===1?"line":"lines" } added, ${ d21() } ${ d21()===1?"line":"lines" } removed
${ B }`)
}

function KW2() {
  VW2.useEffect(()  = > {
    let A  =  ()  = > {
      if (v31()) process.stdout.write(`
` + ha1() + `
`);
      let B  =  E9();
      j6({
        ...B,
        lastCost: wH(),
        lastAPIDuration: wk(),
        lastDuration: kU1(),
        lastLinesAdded: m21(),
        lastLinesRemoved: d21(),
        lastTotalInputTokens: oGA(),
        lastTotalOutputTokens: tGA(),
        lastTotalCacheCreationInputTokens: ADA(),
        lastTotalCacheReadInputTokens: eGA(),
        lastSessionId: B5
      })
    };
    return process.on("exit", A), ()  = > {
      process.off("exit", A)
    }
  }, [])
}

function Ot6(messages, systemPrompt) {
  return Math.round(A * B) / B
}

function HW2(messages, systemPrompt, userPrompt, tools, signal) {
  rGA(A, B, Q, I, G), XW2.add(A, {
    model: G
  }), Kt.add(I.input_tokens, {
    type: "input",
    model: G
  }), Kt.add(I.output_tokens, {
    type: "output",
    model: G
  }), Kt.add(I.cache_read_input_tokens ?? 0, {
    type: "cacheRead",
    model: G
  }), Kt.add(I.cache_creation_input_tokens ?? 0, {
    type: "cacheCreation",
    model: G
  })
}
var _7  =  J1(_1(), 1);
var ma1  =  J1(_1(), 1);
var wX1  =  !1,
  Tt6  =  b0(async function(A) {
    let B  =  await kV({
        apiKey: A,
        maxRetries: 0,
        isNonInteractiveSession: !0
      }),
      {
        response: Q
      }  =  await B.models.list({
        limit: 1
      }).withResponse();
    return Q.headers.get("anthropic-organization-id")
  });
async function EX1() {
  try {
    if (isDebugMode()) return !1;
    let A  =  VA().oauthAccount;
    if (!A) return !1;
    let B  =  aI(!1);
    if (!B) return !1;
    let Q  =  A.organizationUuid;
    if (!Q) {
      if (Q  =  await Tt6(B), !Q) return !1
    }
    let I  =  await K5.get(`https://api.anthropic.com/api/organizations/${ Q }/claude_code_data_sharing`, {
      headers: {
        "Content-Type": "application/json",
        "User-Agent": RL(),
        "x-api-key": B
      }
    });
    if (I.status === 200) {
      let G  =  I.data.claude_code_data_sharing_enabled;
      if (VA().isQualifiedForDataSharing !== G) T0({
        ...VA(),
        isQualifiedForDataSharing: G
      }), wX1  =  !1;
      return G
    }
    return logMetric("tengu_data_sharing_response_err", {  // 记录性能指标
      responseStatus: I.status
    }), !1
  } catch (A) {
    return logError(A), !1  // 记录错误信息
  }
}

function Ht() {
  if (process.env.IS_DEMO) return !1;
  return VA().isQualifiedForDataSharing ?? !1
}

function Pt6() {
  wX1  =  !0;
  let A  =  VA();
  if (A.initialDataSharingMessageSeen) return;
  T0({
    ...A,
    initialDataSharingMessageSeen: !0
  })
}

function zW2() {
  if (wX1) return !1;
  return Ht()
}

function St6() {
  let A  =  $1();
  return ma1.useEffect(()  = > {
    Pt6()
  }, []), _7.createElement(m, {
    flexDirection: "column",
    gap: 1,
    paddingLeft: 1,
    paddingTop: 1
  }, _7.createElement(y, {
      color: A.text
    }, "Your organization has enrolled in the", " ", _7.createElement(M7, {
      url: "https://support.anthropic.com/en/articles/********-about-the-development-partner-program"
    }, "Development Partner Program"),
    ". Your Claude Code sessions are being shared with Anthropic to improve our services including model training. Questions? Contact your account",
    " ", _7.createElement(M7, {
      url: "https://console.anthropic.com/settings/members"
    }, "admin"), "."))
}

function wW2(messages) {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX) return !1;
  return [QU.firstParty, vX.firstParty, ZP.firstParty, YP.firstParty].includes(A)
}

function _t6() {
  let A  =  $1();
  return ma1.useEffect(()  = > {
    wX1  =  !0
  }, []), _7.createElement(m, {
    flexDirection: "column",
    gap: 1,
    paddingLeft: 1,
    paddingTop: 1
  }, _7.createElement(y, {
    color: A.text
  }, "Enrolled in", " ", _7.createElement(M7, {
    url: "https://support.anthropic.com/en/articles/********-about-the-development-partner-program"
  }, "Development Partner Program")))
}

function EW2() {
  return VA().initialDataSharingMessageSeen ? _7.createElement(_t6, null) : _7.createElement(St6, null)
}

function jt6(messages, systemPrompt) {
  return {
    inputTokens: A.inputTokens + B.inputTokens,
    outputTokens: A.outputTokens + B.outputTokens,
    promptCacheWriteTokens: A.promptCacheWriteTokens + B.promptCacheWriteTokens,
    promptCacheReadTokens: A.promptCacheReadTokens + B.promptCacheReadTokens
  }
}
var UW2  =  {
    [rX(Kl.firstParty)]: {
      inputTokens: 0.8,
      outputTokens: 4,
      promptCacheWriteTokens: 1,
      promptCacheReadTokens: 0.08
    },
    [rX(YP.firstParty)]: {
      inputTokens: 3,
      outputTokens: 15,
      promptCacheWriteTokens: 3.75,
      promptCacheReadTokens: 0.3
    },
    [rX(ZP.firstParty)]: {
      inputTokens: 3,
      outputTokens: 15,
      promptCacheWriteTokens: 3.75,
      promptCacheReadTokens: 0.3
    },
    [rX(QU.firstParty)]: {
      inputTokens: 15,
      outputTokens: 75,
      promptCacheWriteTokens: 18.75,
      promptCacheReadTokens: 1.5
    },
    [rX(vX.firstParty)]: {
      inputTokens: 3,
      outputTokens: 15,
      promptCacheWriteTokens: 3.75,
      promptCacheReadTokens: 0.3
    },
    ...!1
  },
  yt6  =  {
    inputTokens: -0.9,
    outputTokens: 0,
    promptCacheReadTokens: -0.09,
    promptCacheWriteTokens: -1.125
  };

function NW2(messages, systemPrompt) {
  return B.input_tokens / 1e6 * A.inputTokens + B.output_tokens / 1e6 * A.outputTokens + (B.cache_read_input_tokens ??
    0) / 1e6 * A.promptCacheReadTokens + (B.cache_creation_input_tokens ?? 0) / 1e6 * A.promptCacheWriteTokens
}

function $W2(A, B) {
  let Q  =  UW2[rX(A)];
  if (!Q) logMetric("tengu_unknown_model_cost", {  // 记录性能指标
    model: A,
    shortName: rX(A)
  }), fU1(), Q  =  UW2[rX(Uj1)];
  let I  =  NW2(Q, B),
    G  =  I;
  if (Ht() && wW2(A)) {
    let D  =  jt6(Q, yt6);
    logMetric("tengu_model_cost_discount", {  // 记录性能指标
      model: A
    }), G  =  NW2(D, B)
  }
  return {
    stickerCostUSD: I,
    finalCostUSD: G
  }
}

function da1({
  model: messages,
  messagesLength: systemPrompt,
  temperature: userPrompt,
  betas: I
}) {
  logMetric("tengu_api_query", {  // 记录性能指标
    model: A,
    messagesLength: B,
    temperature: Q,
    provider: fX(),
    ...I?.length ? {
      betas: I.join(", ")
    } : { }
  })
}

function ua1({
  error: messages,
  model: systemPrompt,
  messageCount: userPrompt,
  messageTokens: tools,
  durationMs: signal,
  durationMsIncludingRetries: options,
  attempt: temperature,
  requestId: Y
}) {
  let W  =  A instanceof Error ? A.message : String(A),
    F  =  A instanceof Q5 ? String(A.status) : void 0;
  logError(A), logMetric("tengu_api_error", {  // 记录性能指标
    model: B,
    error: W,
    status: F,
    messageCount: Q,
    messageTokens: I,
    durationMs: G,
    durationMsIncludingRetries: D,
    attempt: Z,
    provider: fX(),
    requestId: Y || void 0
  })
}

function kt6({
  model: messages,
  messageCount: systemPrompt,
  messageTokens: userPrompt,
  usage: tools,
  durationMs: signal,
  durationMsIncludingRetries: options,
  attempt: temperature,
  ttftMs: response,
  requestId: content,
  stopReason: enableCaching,
  stickerCostUSD: toolChoice,
  costUSD: C
}) {
  logMetric("tengu_api_success", {  // 记录性能指标
    model: A,
    messageCount: B,
    messageTokens: Q,
    inputTokens: I.input_tokens,
    outputTokens: I.output_tokens,
    cachedInputTokens: I.cache_read_input_tokens ?? 0,
    uncachedInputTokens: I.cache_creation_input_tokens ?? 0,
    durationMs: G,
    durationMsIncludingRetries: D,
    attempt: Z,
    ttftMs: Y,
    provider: fX(),
    requestId: W || void 0,
    stop_reason: F || void 0,
    stickerCostUSD: J,
    costUSD: C
  })
}

function xt6(messages, systemPrompt, userPrompt) {
  let {
    stickerCostUSD: I,
    finalCostUSD: G
  }  =  $W2(A.model, A.usage), D  =  Date.now() - B, Z  =  Date.now() - Q;
  return HW2(G, Z, D, A.usage, A.model), {
    stickerCostUSD: I,
    costUSD: G,
    durationMs: D,
    durationMsIncludingRetries: Z
  }
}

function pa1({
  response: messages,
  start: systemPrompt,
  startIncludingRetries: userPrompt,
  attempt: tools,
  messageCount: signal,
  messageTokens: options,
  requestId: Z
}) {
  let {
    stickerCostUSD: Y,
    costUSD: W,
    durationMs: F,
    durationMsIncludingRetries: J
  }  =  xt6(A, B, Q);
  return kt6({
    model: A.model,
    messageCount: G,
    messageTokens: D,
    usage: A.usage,
    durationMs: F,
    durationMsIncludingRetries: J,
    attempt: I,
    ttftMs: A.ttftMs,
    requestId: Z,
    stopReason: A.stop_reason,
    stickerCostUSD: Y,
    costUSD: W
  }), {
    costUSD: W,
    durationMs: F,
    durationMsIncludingRetries: J
  }
}
var ft6  =  10,
  vt6  =  3,
  bt6  =  500,
  ca1  =  "Repeated 529 Overloaded errors";
class jR extends Error {
  originalError;
  retryContext;
  constructor(A, B) {
    let Q  =  A instanceof Error ? A.message : String(A);
    super(Q);
    this.originalError  =  A;
    this.retryContext  =  B;
    if (this.name  =  "RetryError", A instanceof Error && A.stack) this.stack  =  A.stack
  }
}
async function zt(messages, systemPrompt, userPrompt) {
  let I  =  Q.maxRetries ?? ft6,
    G, D  =  {
      model: Q.model
    },
    Z  =  0,
    Y  =  null;
  for (let W  =  1; W <= I + 1; W++) try {
    if (Y === null || G instanceof Q5 && G.status === 401) Y  =  await A();
    return await B(Y, W, D)
  } catch (F) {
    if (G  =  F, ht6(F) && !isDebugMode() && isModelOffSwitchEnabled(Q.model)) {
      if (Z++, Z >= vt6) throw logMetric("tengu_api_custom_529_overloaded_error", { }), new jR(new Error(ca1), D)  // 记录性能指标
    }
    if (W > I || !(F instanceof Q5) || !mt6(F)) throw new jR(F, D);
    if (F instanceof Q5) {
      let X  =  qW2(F);
      if (X) {
        let {
          inputTokens: V,
          contextLimit: K
        }  =  X, U  =  1000, N  =  Math.max(0, K - V - 1000), q  =  Math.max(1, N);
        D.maxTokensOverride  =  q, logMetric("tengu_max_tokens_context_overflow_adjustment", {  // 记录性能指标
          inputTokens: V,
          contextLimit: K,
          adjustedMaxTokens: q,
          attempt: W
        });
        continue
      }
    }
    let J  =  (F.headers?.["retry-after"] || F.headers?.get?.("retry-after")) ?? null,
      C  =  gt6(W, J);
    if (Q.showErrors) {
      if (console.error(
          `  ⎿  ${ wA.red(`API ${ F.name } (${ F.message }) · Retrying in ${ Math.round(C/1000) } seconds… (attempt ${ W }/${ I })`) }`
          ), F.cause instanceof Error) console.error(
        `    ⎿  ${ wA.red(`${ F.cause.name } (${ F.cause.message })${ "code"in F.cause?` (${ F.cause.code })`:"" }`) }`)
    }
    logMetric("tengu_api_retry", {  // 记录性能指标
      attempt: W,
      delayMs: C,
      error: F.message,
      status: F.status,
      provider: fX()
    }), await new Promise((X)  = > setTimeout(X, C))
  }
  throw new jR(G, D)
}

function gt6(messages, systemPrompt) {
  if (B) {
    let G  =  parseInt(B, 10);
    if (!isNaN(G)) return G * 1000
  }
  let Q  =  Math.min(bt6 * Math.pow(2, A - 1), 32000),
    I  =  Math.random() * 0.25 * Q;
  return Q + I
}

function qW2(messages) {
  if (A.status !== 400 || !A.message) return;
  if (!A.message.includes("input length and `max_tokens` exceed context limit")) return;
  let B  =  /input length and `max_tokens` exceed context limit: (\d+) \+ (\d+) > (\d+)/,
    Q  =  A.message.match(B);
  if (!Q || Q.length !== 4) return;
  if (!Q[1] || !Q[2] || !Q[3]) {
    logError(new Error("Unable to parse max_tokens from max_tokens exceed context limit error message"));  // 记录错误信息
    return
  }
  let I  =  parseInt(Q[1], 10),
    G  =  parseInt(Q[2], 10),
    D  =  parseInt(Q[3], 10);
  if (isNaN(I) || isNaN(G) || isNaN(D)) return;
  return {
    inputTokens: I,
    maxTokens: G,
    contextLimit: D
  }
}

function ht6(messages) {
  if (!(A instanceof Q5)) return !1;
  return A.status === 529 || (A.message?.includes('"type":"overloaded_error"') ?? !1)
}

function mt6(messages) {
  if (A.message?.includes('"type":"overloaded_error"')) return !0;
  if (qW2(A)) return !0;
  let B  =  A.headers?.get("x-should-retry");
  if (B === "true" && !isDebugMode()) return !0;
  if (B === "false") return !1;
  if (A instanceof RU) return !0;
  if (!A.status) return !1;
  if (A.status === 408) return !0;
  if (A.status === 409) return !0;
  if (A.status === 429) return !isDebugMode();
  if (A.status === 401) return RyA(), !0;
  if (A.status && A.status >= 500) return !0;
  return !1
}

function NX1(messages) {
  let B  =  { },
    Q  =  process.env.CLAUDE_CODE_EXTRA_BODY,
    I  =  { };
  if (Q) try {
    let D  =  T8(Q);
    if (D && typeof D === "object" && !Array.isArray(D)) I  =  D;
    else _X(`CLAUDE_CODE_EXTRA_BODY env var must be a JSON object, but was given ${ Q }`)
  } catch (D) {
    _X(`Error parsing CLAUDE_CODE_EXTRA_BODY: ${ D instanceof Error?D.message:String(D) }`)
  }
  let G  =  {
    ...B,
    ...I
  };
  if (A && A.length > 0)
    if (G.anthropic_beta && Array.isArray(G.anthropic_beta)) {
      let D  =  G.anthropic_beta,
        Z  =  A.filter((Y)  = > !D.includes(Y));
      G.anthropic_beta  =  [...D, ...Z]
    } else G.anthropic_beta  =  A;
  return G
}
var REFUSAL_PREFIX  =  "API Error",
  Et  =  "Prompt is too long",
  la1  =  "Credit balance is too low",
  $X1  =  "Invalid API key · Please run /login",
  ia1  =  "Claude AI usage limit reached",
  na1  =  "Repeated server overload with Opus model",
  jY  =  "(no content)",
  qX1  =  "OAuth token revoked · Please run /login",
  ENABLE_PROMPT_CACHING  =  !DP(process.env.DISABLE_PROMPT_CACHING),
  DEFAULT_TEMPERATURE  =  1,
  OFF_SWITCH_ERROR_MESSAGE  =  "Opus is experiencing high load, please use /model to switch to Sonnet";

function nr() {
  return {
    user_id: mP()
  }
}
async function RW2(messages, systemPrompt) {
  if (B) return !0;
  try {
    let Q  =  getCurrentModel(),
      I  =  getBetaFeatures(Q);
    return await zt(()  = > kV({
      apiKey: A,
      maxRetries: 3,
      model: Q,
      isNonInteractiveSession: B
    }), async (G)  = > {
      let D  =  [{
        role: "user",
        content: "test"
      }];
      return await G.beta.messages.create({
        model: Q,
        max_tokens: 1,
        messages: D,
        temperature: 0,
        ...I.length > 0 ? {
          betas: I
        } : { },
        metadata: nr(),
        ...NX1()
      }), !0
    }, {
      maxRetries: 2,
      showErrors: !1,
      model: Q
    }), !0
  } catch (Q) {
    let I  =  Q;
    if (Q instanceof jR) I  =  Q.originalError;
    if (logError(I), I instanceof Error && I.message.includes(  // 记录错误信息
        '{ "type":"error", "error":{ "type":"authentication_error", "message":"invalid x-api-key" }}')) return !1;
    throw I
  }
}
async function dt6(messages) {
  let B  =  Date.now(),
    Q, I  =  null;
  for await (let D of A) if (D.type === "message_start") Q  =  Date.now() - B, I  =  {
    cache_read_input_tokens: D.message.usage.cache_read_input_tokens,
    cache_creation_input_tokens: D.message.usage.cache_creation_input_tokens
  };
  let G  =  await A.finalMessage();
  return {
    ...G,
    ttftMs: Q,
    usage: {
      ...G.usage,
      cache_read_input_tokens: G.usage.cache_read_input_tokens ?? I?.cache_read_input_tokens ?? 0,
      cache_creation_input_tokens: G.usage.cache_creation_input_tokens ?? I?.cache_creation_input_tokens ?? 0
    }
  }
}

function formatUserMessage(messages, B  =  !1) {
  if (B)
    if (typeof A.message.content === "string") return {
      role: "user",
      content: [{
        type: "text",
        text: A.message.content,
        ...ENABLE_PROMPT_CACHING ? {
          cache_control: {  // 缓存控制配置
            type: "ephemeral"
          }
        } : { }
      }]
    };
    else return {
      role: "user",
      content: A.message.content.map((Q, I)  = > ({
        ...Q,
        ...I === A.message.content.length - 1 ? ENABLE_PROMPT_CACHING ? {
          cache_control: {  // 缓存控制配置
            type: "ephemeral"
          }
        } : { } : { }
      }))
    };
  return {
    role: "user",
    content: A.message.content
  }
}

function formatAssistantMessage(messages, B  =  !1) {
  if (B)
    if (typeof A.message.content === "string") return {
      role: "assistant",
      content: [{
        type: "text",
        text: A.message.content,
        ...ENABLE_PROMPT_CACHING ? {
          cache_control: {  // 缓存控制配置
            type: "ephemeral"
          }
        } : { }
      }]
    };
    else return {
      role: "assistant",
      content: A.message.content.map((Q, I)  = > ({
        ...Q,
        ...I === A.message.content.length - 1 && Q.type !== "thinking" && Q.type !== "redacted_thinking" ? ENABLE_PROMPT_CACHING ?
        {
          cache_control: {  // 缓存控制配置
            type: "ephemeral"
          }
        } : { } : { }
      }))
    };
  return {
    role: "assistant",
    content: A.message.content
  }
}
async function executeWithRetry(tool, toolUseId, input, context, permissionHandler, message) {
  return awaitResult(withErrorHandling(A, ()  = > streamClaudeAPI(A, B, Q, I, G, D)))
}
async function* streamAPIRequest(A, B, Q, I, G, D) {
  return yield* withErrorHandling(A, ()  = > streamClaudeAPI(A, B, Q, I, G, D))
}
/**
 * Streaming Claude API function for real-time responses
 * Implements tool calling, error handling, and retry logic
 * Tracks cost and performance metrics
 */
/**
 * 流式调用 Claude API 的函数
 * 
 * 功能详情:
 * - 实现实时流式响应处理
 * - 集成工具调用功能
 * - 包含错误处理和重试逻辑
 * - 跟踪成本和性能指标
 * - 支持工具权限验证
 * 
 * @returns {Promise} 返回API调用的Promise对象
 */
async function* streamClaudeAPI(A, B, Q, I, G, D) {
  if (!isDebugMode() && (await getFeatureFlag("tengu-off-switch", {
      activated: !1
    })).activated && isModelOffSwitchEnabled(D.model)) return logMetric("tengu_off_switch_query", { }), createOffSwitchError(new Error(OFF_SWITCH_ERROR_MESSAGE), D.model);  // 记录性能指标
  let [Z, Y]  =  await Promise.all([Promise.all(I.map((f)  = > validateToolPermissions(f, {
    getToolPermissionContext: D.getToolPermissionContext,  // 获取工具权限上下文
    tools: I
  }))), getBetaFeatures(D.model)]);
  if (D.prependCLISysprompt) validateSystemPrompt(B), B  =  [getCLISystemPrompt(), ...B];
  let W  =  buildSystemPromptWithCaching(B),
    F  =  ENABLE_PROMPT_CACHING && Y.length > 0,
    J  =  D.temperature ?? DEFAULT_TEMPERATURE,
    C  =  processMessageHistory(A);
  da1({
    model: D.model,
    messagesLength: JSON.stringify([...W, ...C, ...Z, ...D.extraToolSchemas ?? []]).length,
    temperature: J,
    betas: F ? Y : []
  });
  let X  =  Date.now(),
    V  =  Date.now(),
    K  =  0,
    U  =  void 0,
    N  =  void 0,
    q  =  (f)  = > {
      let a  =  f.maxTokensOverride ? Math.min(Q, f.maxTokensOverride - 1) : Q,
        g;
      if (!DP(process.env.DISABLE_INTERLEAVED_THINKING) && AZ() === "bedrock" && [QU.bedrock, vX.bedrock].includes(f
          .model)) g  =  NX1([h61]);
      else g  =  NX1();
      let Y1  =  Q > 0 ? {
          budget_tokens: a,
          type: "enabled"
        } : void 0,
        r  =  f?.maxTokensOverride || Math.max(Q + 1, getModelTokenLimit(D.model));
      return {
        model: D.model,
        messages: convertMessagesToAPIFormat(C),
        temperature: J,
        system: W,
        tools: [...Z, ...D.extraToolSchemas ?? []],
        tool_choice: D.toolChoice,
        ...F ? {
          betas: Y
        } : { },
        metadata: nr(),
        max_tokens: r,
        thinking: Y1,
        ...g
      }
    },
    M  =  null;
  try {
    ku0().catch((f)  = > {
      logError(f)  // 记录错误信息
    }), U  =  await zt(()  = > kV({
      maxRetries: 0,
      model: D.model,
      isNonInteractiveSession: D.isNonInteractiveSession
    }), async (f, a, g)  = > {
      return K  =  a, V  =  Date.now(), f.beta.messages.stream(q(g), {
        signal: G
      })
    }, {
      showErrors: !D.isNonInteractiveSession,
      model: D.model
    });
    try {
      for await (let f of U) {
        if (f.type === "message_start" && f.message?.usage) M  =  {
          cache_read_input_tokens: f.message.usage.cache_read_input_tokens,
          cache_creation_input_tokens: f.message.usage.cache_creation_input_tokens
        };
        yield {
          type: "stream_event",
          event: f
        }
      }
      N  =  await U.finalMessage(), N  =  {
        ...N,
        usage: {
          ...N.usage,
          cache_read_input_tokens: N.usage.cache_read_input_tokens ?? M?.cache_read_input_tokens ?? 0,
          cache_creation_input_tokens: N.usage.cache_creation_input_tokens ?? M?.cache_creation_input_tokens ?? 0
        }
      }
    } catch (f) {
      if (f instanceof QI) throw f;
      logError(f), logMetric("tengu_streaming_error_fallback", {  // 记录性能指标
        error: f instanceof Error ? f.message : String(f),
        model: D.model
      }), N  =  await zt(()  = > kV({
        maxRetries: 0,
        model: D.model,
        isNonInteractiveSession: D.isNonInteractiveSession
      }), async (g, Y1, r)  = > {
        return K  =  Y1, await g.beta.messages.create(q(r))
      }, {
        showErrors: !D.isNonInteractiveSession,
        model: D.model
      })
    }
  } catch (f) {
    let a  =  f,
      g  =  D.model;
    if (f instanceof jR) a  =  f.originalError, g  =  f.retryContext.model;
    return ua1({
      error: a,
      model: g,
      messageCount: C.length,
      messageTokens: HN(C),
      durationMs: Date.now() - V,
      durationMsIncludingRetries: Date.now() - X,
      attempt: K,
      requestId: U?.request_id
    }), createOffSwitchError(a, g)
  }
  if (!N || !hu0(N)) {
    let f  =  mu0(N);
    return createOffSwitchError(new Error(`Failed to get response from API${ f?` (${ f })`:"" }`), D.model)
  }
  let {
    costUSD: R,
    durationMs: T
  }  =  pa1({
    response: N,
    start: V,
    startIncludingRetries: X,
    attempt: K,
    messageCount: C.length,
    messageTokens: HN(C),
    requestId: U?.request_id
  }), O  =  handleRefusalResponse(N);
  if (O) return O;
  return {
    message: {
      ...N,
      content: gF1(N.content)
    },
    costUSD: R,
    durationMs: T,
    type: "assistant",
    uuid: MW2(),
    timestamp: new Date().toISOString()
  }
}

function createOffSwitchError(messages, systemPrompt) {
  if (A instanceof Error && A.message.includes(ca1)) return KC({
    content: na1
  });
  if (A instanceof Error && A.message.includes(OFF_SWITCH_ERROR_MESSAGE)) return KC({
    content: OFF_SWITCH_ERROR_MESSAGE
  });
  if (A instanceof Q5 && A.status === 429 && isDebugMode()) {
    let Q  =  A.headers?.get?.("anthropic-ratelimit-unified-reset"),
      I  =  Number(Q) || 0,
      G  =  `${ ia1 }|${ I }`;
    return KC({
      content: G
    })
  }
  if (A instanceof Error && A.message.includes("prompt is too long")) return KC({
    content: Et
  });
  if (A instanceof Error && A.message.includes("Your credit balance is too low")) return KC({
    content: la1
  });
  if (A instanceof Error && A.message.toLowerCase().includes("x-api-key")) return KC({
    content: $X1
  });
  if (A instanceof Q5 && A.status === 403 && A.message.includes("OAuth token has been revoked")) return KC({
    content: qX1
  });
  if (process.env.CLAUDE_CODE_USE_BEDROCK && A instanceof Error && A.message.toLowerCase().includes("model id"))
  return KC({
      content: `${ REFUSAL_PREFIX } (${ B }): ${ A.message }`
    });
  if (A instanceof Error) return KC({
    content: `${ REFUSAL_PREFIX }: ${ A.message }`
  });
  return KC({
    content: REFUSAL_PREFIX
  })
}

// ==================== 消息格式化 ====================

function convertMessagesToAPIFormat(messages) {
  return A.map((B, Q)  = > {
    return B.type === "user" ? formatUserMessage(B, Q > A.length - 3) : formatAssistantMessage(B, Q > A.length - 3)
  })
}
async function makeAPIRequest({
  systemPrompt: messages,
  userPrompt: systemPrompt,
  assistantPrompt: userPrompt,
  signal: tools,
  isNonInteractiveSession: signal,
  temperature: D  =  0,
  enablePromptCaching: Z
}) {
  let Y  =  getCurrentModel(),
    W  =  [{
      role: "user",
      content: B
    }, ...Q ? [{
      role: "assistant",
      content: Q
    }] : []],
    F  =  buildSystemPromptWithCaching(A, Z && ENABLE_PROMPT_CACHING),
    J  =  Z ? [...F, ...W] : [{
      systemPrompt: A
    }, ...W];
  da1({
    model: Y,
    messagesLength: JSON.stringify(J).length,
    temperature: D
  });
  let C  =  0,
    X  =  Date.now(),
    V  =  Date.now(),
    K, U, N  =  getBetaFeatures(Y);
  try {
    K  =  await zt(()  = > kV({
      maxRetries: 0,
      model: Y,
      isNonInteractiveSession: G
    }), async (S, f, a)  = > {
      return C  =  f, X  =  Date.now(), U  =  S.beta.messages.stream({
        model: a.model,
        max_tokens: 512,
        messages: W,
        system: F,
        temperature: D,
        metadata: nr(),
        stream: !0,
        ...N.length > 0 ? {
          betas: N
        } : { },
        ...NX1()
      }, {
        signal: I
      }), await dt6(U)
    }, {
      showErrors: !1,
      model: Y
    })
  } catch (S) {
    let f  =  S,
      a  =  Y;
    if (S instanceof jR) f  =  S.originalError, a  =  S.retryContext.model;
    return ua1({
      error: f,
      model: a,
      messageCount: Q ? 2 : 1,
      durationMs: Date.now() - X,
      durationMsIncludingRetries: Date.now() - V,
      attempt: C,
      requestId: U?.request_id
    }), createOffSwitchError(f, a)
  }
  let q  =  handleRefusalResponse(K);
  if (q) return q;
  let {
    costUSD: M,
    durationMs: R
  }  =  pa1({
    response: K,
    start: X,
    startIncludingRetries: V,
    attempt: C,
    messageCount: Q ? 2 : 1,
    requestId: U?.request_id
  }), T  =  Z ? {
    ...K,
    content: gF1(K.content)
  } : {
    ...K,
    content: gF1(K.content),
    usage: {
      ...K.usage,
      cache_read_input_tokens: 0,
      cache_creation_input_tokens: 0
    }
  };
  return {
    durationMs: R,
    message: T,
    costUSD: M,
    uuid: MW2(),
    type: "assistant",
    timestamp: new Date().toISOString()
  }
}

function buildSystemPromptWithCaching(messages, B  =  ENABLE_PROMPT_CACHING) {
  return Xp1(A).map((Q)  = > ({
    type: "text",
    text: Q,
    ...B ? {
      cache_control: {  // 缓存控制配置
        type: "ephemeral"
      }
    } : { }
  }))
}
/**
 * Core Claude API calling function
 * Handles system prompts, user prompts, and assistant prompts
 * Supports prompt caching, temperature control, and signal interruption
 */
// ==================== 核心 API 函数 ====================

/**
 * 调用 Claude API 的核心函数
 * 
 * 功能详情:
 * - 处理系统提示词、用户提示词和助手提示词
 * - 支持提示词缓存以提高性能
 * - 支持温度参数控制响应随机性
 * - 支持信号中断机制
 * - 支持非交互式会话模式
 * 
 * 参数说明:
 * @param {systemPrompt} 系统提示词数组，定义AI的行为和角色
 * @param {userPrompt} 用户输入的提示词
 * @param {assistantPrompt} 助手的预设回复
 * @param {enablePromptCaching} 是否启用提示词缓存
 * @param {signal} 中断信号，用于取消请求
 * @param {isNonInteractiveSession} 是否为非交互式会话
 * @param {temperature} 温度参数，控制响应的随机性(0-1)
 * 
 * @returns {Promise} 返回API调用的Promise对象
 */
async function callClaudeAPI({
  systemPrompt: A  =  [],
  userPrompt: systemPrompt,
  assistantPrompt: userPrompt,
  enablePromptCaching: I  =  !1,
  signal: signal,
  isNonInteractiveSession: options,
  temperature: Z  =  0
}) {
  return await withRetry([createMessage({  // 创建消息对象
    content: A.map((Y)  = > ({
      type: "text",
      text: Y
    }))
  }), createMessage({  // 创建消息对象
    content: B
  })], ()  = > makeAPIRequest({
    systemPrompt: A,
    userPrompt: B,
    assistantPrompt: Q,
    signal: G,
    isNonInteractiveSession: D,
    temperature: Z,
    enablePromptCaching: I
  }))
}

function getModelTokenLimit(messages) {
  if (A.includes("3-5")) return 8192;
  if (A.includes("haiku")) return 8192;
  return 20000
}

function handleRefusalResponse(messages) {
  if (A.stop_reason !== "refusal") return;
  return logMetric("tengu_refusal_api_response", { }), KC({  // 记录性能指标
    content: `${ REFUSAL_PREFIX }: Claude Code is unable to respond to this request, which appears to violate our Usage Policy (https://www.anthropic.com/legal/aup). Please double press esc to edit your last message or start a new session for Claude Code to assist with a different task.`
  })
}
var h4  =  J1(_1(), 1);
import {
  EOL as He6
} from "os";
import {
  dirname as ze6,
  extname as we6,
  isAbsolute as Ee6,
  relative as Bs1,
  resolve as Ue6,
  sep as Ne6
} from "path";
var Y4  =  J1(_1(), 1);

function hY(messages, systemPrompt) {
  return A.flatMap((Q, I)  = > I ? [B(I), Q] : [Q])
}
var L9  =  J1(_1(), 1);

function sV() { }
sV.prototype  =  {
  diff: function A(systemPrompt, userPrompt) {
    var I, G  =  arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : { },
      D  =  G.callback;
    if (typeof G === "function") D  =  G, G  =  { };
    var Z  =  this;

    function Y(queuedCommands) {
      if (T  =  Z.postProcess(T, G), D) return setTimeout(function() {
        D(T)
      }, 0), !0;
      else return T
    }
    B  =  this.castInput(B, G), Q  =  this.castInput(Q, G), B  =  this.removeEmpty(this.tokenize(B, G)), Q  =  this
      .removeEmpty(this.tokenize(Q, G));
    var W  =  Q.length,
      F  =  B.length,
      J  =  1,
      C  =  W + F;
    if (G.maxEditLength  !=  null) C  =  Math.min(C, G.maxEditLength);
    var X  =  (I  =  G.timeout) !== null && I !== void 0 ? I : 1 / 0,
      V  =  Date.now() + X,
      K  =  [{
        oldPos: -1,
        lastComponent: void 0
      }],
      U  =  this.extractCommon(K[0], Q, B, 0, G);
    if (K[0].oldPos + 1 >= F && U + 1 >= W) return Y(SW2(Z, K[0].lastComponent, Q, B, Z.useLongestToken));
    var N  =  -1 / 0,
      q  =  1 / 0;

    function M() {
      for (var T  =  Math.max(N, -J); T <= Math.min(q, J); T + =  2) {
        var O  =  void 0,
          S  =  K[T - 1],
          f  =  K[T + 1];
        if (S) K[T - 1]  =  void 0;
        var a  =  !1;
        if (f) {
          var g  =  f.oldPos - T;
          a  =  f && 0 <= g && g < W
        }
        var Y1  =  S && S.oldPos + 1 < F;
        if (!a && !Y1) {
          K[T]  =  void 0;
          continue
        }
        if (!Y1 || a && S.oldPos < f.oldPos) O  =  Z.addToPath(f, !0, !1, 0, G);
        else O  =  Z.addToPath(S, !1, !0, 1, G);
        if (U  =  Z.extractCommon(O, Q, B, T, G), O.oldPos + 1 >= F && U + 1 >= W) return Y(SW2(Z, O.lastComponent, Q,
          B, Z.useLongestToken));
        else {
          if (K[T]  =  O, O.oldPos + 1 >= F) q  =  Math.min(q, T - 1);
          if (U + 1 >= W) N  =  Math.max(N, T + 1)
        }
      }
      J++
    }
    if (D)(function T() {
      setTimeout(function() {