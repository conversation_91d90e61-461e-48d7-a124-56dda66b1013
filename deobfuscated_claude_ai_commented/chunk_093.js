/**
 * ================================================================
 * MCP 和外部工具集成模块
 * ================================================================
 * 
 * 处理外部工具集成和模型上下文协议
 * 
 * 主要功能:
 * - 模型上下文协议(MCP)支持
 * - 外部工具发现和管理
 * - 工具权限和安全控制
 * - 动态工具集成
 * 
 * 文件: chunk_093.js
 * 版本: Claude Code CLI v1.0.3
 * 
 * 注意: 此文件已从混淆代码反混淆并添加中文注释
 * ================================================================
 */

/**
 * Claude AI Integration - Deobfuscated Code
 *
 * This file contains the core AI integration logic for Claude Code CLI.
 * Functions have been renamed for clarity and comments added for understanding.
 *
 * Key components:
 * - API calling functions (callClaudeAPI, streamClaudeAPI)
 * - Conversation loop management (mainConversationLoop, coreConversationHandler)
 * - Tool execution and validation
 * - Message formatting and context management
 * - Error handling and retry logic
 */

// Chunk 93
// Lines 279001-282000
// Size: 83285 bytes

    defaultPrevented: A.defaultPrevented,
    cancelable: A.cancelable,
    timeStamp: A.timeStamp
  }
}
var fZ0  =  (A)  = > {
    throw TypeError(A)
  },
  rx1  =  (A, B, Q)  = > B.has(A) || fZ0("Cannot " + Q),
  C6  =  (A, B, Q)  = > (rx1(A, B, "read from private field"), Q ? Q.call(A) : B.get(A)),
  L7  =  (A, B, Q)  = > B.has(A) ? fZ0("Cannot add the same private member more than once") : B instanceof WeakSet ? B.add(
    A) : B.set(A, Q),
  hB  =  (A, B, Q, I)  = > (rx1(A, B, "write to private field"), B.set(A, Q), Q),
  xU  =  (A, B, Q)  = > (rx1(A, B, "access private method"), Q),
  EY, JS, qb, aI1, sI1, Va, Rb, Ka, AL, Mb, Ob, Lb, Ca, VV, px1, cx1, lx1, xZ0, ix1, nx1, Xa, ax1, sx1;
class Tb extends EventTarget {
  constructor(A, B) {
    var Q, I;
    super(), L7(this, VV), this.CONNECTING  =  0, this.OPEN  =  1, this.CLOSED  =  2, L7(this, EY), L7(this, JS), L7(this,
        qb), L7(this, aI1), L7(this, sI1), L7(this, Va), L7(this, Rb), L7(this, Ka, null), L7(this, AL), L7(this, Mb),
      L7(this, Ob, null), L7(this, Lb, null), L7(this, Ca, null), L7(this, cx1, async (G)  = > {
        var D;
        C6(this, Mb).reset();
        let {
          body: Z,
          redirected: Y,
          status: W,
          headers: F
        }  =  G;
        if (W === 204) {
          xU(this, VV, Xa).call(this, "Server sent HTTP 204, not reconnecting", 204), this.close();
          return
        }
        if (Y ? hB(this, qb, new URL(G.url)) : hB(this, qb, void 0), W !== 200) {
          xU(this, VV, Xa).call(this, `Non-200 status code (${ W })`, W);
          return
        }
        if (!(F.get("content-type") || "").startsWith("text/event-stream")) {
          xU(this, VV, Xa).call(this, 'Invalid content type, expected "text/event-stream"', W);
          return
        }
        if (C6(this, EY) === this.CLOSED) return;
        hB(this, EY, this.OPEN);
        let J  =  new Event("open");
        if ((D  =  C6(this, Ca))  ==  null || D.call(this, J), this.dispatchEvent(J), typeof Z  !=  "object" || !Z || !(
            "getReader" in Z)) {
          xU(this, VV, Xa).call(this, "Invalid response body, expected a web ReadableStream", W), this.close();
          return
        }
        let C  =  new TextDecoder,
          X  =  Z.getReader(),
          V  =  !0;
        do {
          let {
            done: K,
            value: U
          }  =  await X.read();
          U && C6(this, Mb).feed(C.decode(U, {
            stream: !K
          })), K && (V  =  !1, C6(this, Mb).reset(), xU(this, VV, ax1).call(this))
        } while (V)
      }), L7(this, lx1, (G)  = > {
        hB(this, AL, void 0), !(G.name === "AbortError" || G.type === "aborted") && xU(this, VV, ax1).call(this,
          ux1(G))
      }), L7(this, ix1, (G)  = > {
        typeof G.id  ==  "string" && hB(this, Ka, G.id);
        let D  =  new MessageEvent(G.event || "message", {
          data: G.data,
          origin: C6(this, qb) ? C6(this, qb).origin : C6(this, JS).origin,
          lastEventId: G.id || ""
        });
        C6(this, Lb) && (!G.event || G.event === "message") && C6(this, Lb).call(this, D), this.dispatchEvent(D)
      }), L7(this, nx1, (G)  = > {
        hB(this, Va, G)
      }), L7(this, sx1, ()  = > {
        hB(this, Rb, void 0), C6(this, EY) === this.CONNECTING && xU(this, VV, px1).call(this)
      });
    try {
      if (A instanceof URL) hB(this, JS, A);
      else if (typeof A  ==  "string") hB(this, JS, new URL(A, T_4()));
      else throw new Error("Invalid URL")
    } catch {
      throw O_4("An invalid or illegal string was specified")
    }
    hB(this, Mb, yZ0({
        onEvent: C6(this, ix1),
        onRetry: C6(this, nx1)
      })), hB(this, EY, this.CONNECTING), hB(this, Va, 3000), hB(this, sI1, (Q  =  B  ==  null ? void 0 : B.fetch)  != 
        null ? Q : globalThis.fetch), hB(this, aI1, (I  =  B  ==  null ? void 0 : B.withCredentials)  !=  null ? I : !1),
      xU(this, VV, px1).call(this)
  }
  get readyState() {
    return C6(this, EY)
  }
  get url() {
    return C6(this, JS).href
  }
  get withCredentials() {
    return C6(this, aI1)
  }
  get onerror() {
    return C6(this, Ob)
  }
  set onerror(A) {
    hB(this, Ob, A)
  }
  get onmessage() {
    return C6(this, Lb)
  }
  set onmessage(A) {
    hB(this, Lb, A)
  }
  get onopen() {
    return C6(this, Ca)
  }
  set onopen(A) {
    hB(this, Ca, A)
  }
  addEventListener(A, B, Q) {
    let I  =  B;
    super.addEventListener(A, I, Q)
  }
  removeEventListener(A, B, Q) {
    let I  =  B;
    super.removeEventListener(A, I, Q)
  }
  close() {
    C6(this, Rb) && clearTimeout(C6(this, Rb)), C6(this, EY) !== this.CLOSED && (C6(this, AL) && C6(this, AL).abort(),
      hB(this, EY, this.CLOSED), hB(this, AL, void 0))
  }
}
EY  =  new WeakMap, JS  =  new WeakMap, qb  =  new WeakMap, aI1  =  new WeakMap, sI1  =  new WeakMap, Va  =  new WeakMap, Rb  = 
  new WeakMap, Ka  =  new WeakMap, AL  =  new WeakMap, Mb  =  new WeakMap, Ob  =  new WeakMap, Lb  =  new WeakMap, Ca  = 
  new WeakMap, VV  =  new WeakSet, px1  =  function() {
    hB(this, EY, this.CONNECTING), hB(this, AL, new AbortController), C6(this, sI1)(C6(this, JS), xU(this, VV, xZ0)
      .call(this)).then(C6(this, cx1)).catch(C6(this, lx1))
  }, cx1  =  new WeakMap, lx1  =  new WeakMap, xZ0  =  function() {
    var A;
    let B  =  {
      mode: "cors",
      redirect: "follow",
      headers: {
        Accept: "text/event-stream",
        ...C6(this, Ka) ? {
          "Last-Event-ID": C6(this, Ka)
        } : void 0
      },
      cache: "no-store",
      signal: (A  =  C6(this, AL))  ==  null ? void 0 : A.signal
    };
    return "window" in globalThis && (B.credentials  =  this.withCredentials ? "include" : "same-origin"), B
  }, ix1  =  new WeakMap, nx1  =  new WeakMap, Xa  =  function(A, B) {
    var Q;
    C6(this, EY) !== this.CLOSED && hB(this, EY, this.CLOSED);
    let I  =  new dx1("error", {
      code: B,
      message: A
    });
    (Q  =  C6(this, Ob))  ==  null || Q.call(this, I), this.dispatchEvent(I)
  }, ax1  =  function(A, B) {
    var Q;
    if (C6(this, EY) === this.CLOSED) return;
    hB(this, EY, this.CONNECTING);
    let I  =  new dx1("error", {
      code: B,
      message: A
    });
    (Q  =  C6(this, Ob))  ==  null || Q.call(this, I), this.dispatchEvent(I), hB(this, Rb, setTimeout(C6(this, sx1), C6(
      this, Va)))
  }, sx1  =  new WeakMap, Tb.CONNECTING  =  0, Tb.OPEN  =  1, Tb.CLOSED  =  2;

function T_4() {
  let A  =  "document" in globalThis ? globalThis.document : void 0;
  return A && typeof A  ==  "object" && "baseURI" in A && typeof A.baseURI  ==  "string" ? A.baseURI : void 0
}
var ox1;
ox1  =  globalThis.crypto?.webcrypto ?? globalThis.crypto ?? import("node:crypto").then((A)  = > A.webcrypto);
async function P_4(messages) {
  return (await ox1).getRandomValues(new Uint8Array(A))
}
async function S_4(messages) {
  let Q  =  "",
    I  =  await P_4(A);
  for (let G  =  0; G < A; G++) {
    let D  =  I[G] % 66;
    Q + =  "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~" [D]
  }
  return Q
}
async function __4(messages) {
  return await S_4(A)
}
async function j_4(messages) {
  let B  =  await (await ox1).subtle.digest("SHA-256", new TextEncoder().encode(A));
  return btoa(String.fromCharCode(...new Uint8Array(B))).replace(/\//g, "_").replace(/\+/g, "-").replace(/ = /g, "")
}
async function tx1(messages) {
  if (!A) A  =  43;
  if (A < 43 || A > 128) throw `Expected a length between 43 and 128. Received ${ A }.`;
  let B  =  await __4(A),
    Q  =  await j_4(B);
  return {
    code_verifier: B,
    code_challenge: Q
  }
}
var rI1  =  i.object({
    issuer: i.string(),
    authorization_endpoint: i.string(),
    token_endpoint: i.string(),
    registration_endpoint: i.string().optional(),
    scopes_supported: i.array(i.string()).optional(),
    response_types_supported: i.array(i.string()),
    response_modes_supported: i.array(i.string()).optional(),
    grant_types_supported: i.array(i.string()).optional(),
    token_endpoint_auth_methods_supported: i.array(i.string()).optional(),
    token_endpoint_auth_signing_alg_values_supported: i.array(i.string()).optional(),
    service_documentation: i.string().optional(),
    revocation_endpoint: i.string().optional(),
    revocation_endpoint_auth_methods_supported: i.array(i.string()).optional(),
    revocation_endpoint_auth_signing_alg_values_supported: i.array(i.string()).optional(),
    introspection_endpoint: i.string().optional(),
    introspection_endpoint_auth_methods_supported: i.array(i.string()).optional(),
    introspection_endpoint_auth_signing_alg_values_supported: i.array(i.string()).optional(),
    code_challenge_methods_supported: i.array(i.string()).optional()
  }).passthrough(),
  Ha  =  i.object({
    access_token: i.string(),
    token_type: i.string(),
    expires_in: i.number().optional(),
    scope: i.string().optional(),
    refresh_token: i.string().optional()
  }).strip(),
  vZ0  =  i.object({
    error: i.string(),
    error_description: i.string().optional(),
    error_uri: i.string().optional()
  }),
  bZ0  =  i.object({
    redirect_uris: i.array(i.string()).refine((A)  = > A.every((B)  = > URL.canParse(B)), {
      message: "redirect_uris must contain valid URLs"
    }),
    token_endpoint_auth_method: i.string().optional(),
    grant_types: i.array(i.string()).optional(),
    response_types: i.array(i.string()).optional(),
    client_name: i.string().optional(),
    client_uri: i.string().optional(),
    logo_uri: i.string().optional(),
    scope: i.string().optional(),
    contacts: i.array(i.string()).optional(),
    tos_uri: i.string().optional(),
    policy_uri: i.string().optional(),
    jwks_uri: i.string().optional(),
    jwks: i.any().optional(),
    software_id: i.string().optional(),
    software_version: i.string().optional()
  }).strip(),
  y_4  =  i.object({
    client_id: i.string(),
    client_secret: i.string().optional(),
    client_id_issued_at: i.number().optional(),
    client_secret_expires_at: i.number().optional()
  }).strip(),
  ex1  =  bZ0.merge(y_4),
  k_4  =  i.object({
    error: i.string(),
    error_description: i.string().optional()
  }).strip(),
  kG8  =  i.object({
    token: i.string(),
    token_type_hint: i.string().optional()
  }).strip();
class CS extends Error {
  constructor(A) {
    super(A !== null && A !== void 0 ? A : "Unauthorized")
  }
}
async function oI1(messages, {
  serverUrl: systemPrompt,
  authorizationCode: Q
}) {
  let I  =  await x_4(B),
    G  =  await Promise.resolve(A.clientInformation());
  if (!G) {
    if (Q !== void 0) throw new Error(
      "Existing OAuth client information is required when exchanging an authorization code");
    if (!A.saveClientInformation) throw new Error(
      "OAuth client information must be saveable for dynamic registration");
    let W  =  await g_4(B, {
      metadata: I,
      clientMetadata: A.clientMetadata
    });
    await A.saveClientInformation(W), G  =  W
  }
  if (Q !== void 0) {
    let W  =  await A.codeVerifier(),
      F  =  await v_4(B, {
        metadata: I,
        clientInformation: G,
        authorizationCode: Q,
        codeVerifier: W,
        redirectUri: A.redirectUrl
      });
    return await A.saveTokens(F), "AUTHORIZED"
  }
  let D  =  await A.tokens();
  if (D === null || D === void 0 ? void 0 : D.refresh_token) try {
    let W  =  await b_4(B, {
      metadata: I,
      clientInformation: G,
      refreshToken: D.refresh_token
    });
    return await A.saveTokens(W), "AUTHORIZED"
  } catch (W) {
    console.error("Could not refresh OAuth tokens:", W)
  }
  let {
    authorizationUrl: Z,
    codeVerifier: Y
  }  =  await f_4(B, {
    metadata: I,
    clientInformation: G,
    redirectUrl: A.redirectUrl
  });
  return await A.saveCodeVerifier(Y), await A.redirectToAuthorization(Z), "REDIRECT"
}
async function x_4(messages, systemPrompt) {
  var Q;
  let I  =  new URL("/.well-known/oauth-authorization-server", A),
    G;
  try {
    G  =  await fetch(I, {
      headers: {
        "MCP-Protocol-Version": (Q  =  B === null || B === void 0 ? void 0 : B.protocolVersion) !== null && Q !==
          void 0 ? Q : FS
      }
    })
  } catch (D) {
    if (D instanceof TypeError) G  =  await fetch(I);
    else throw D
  }
  if (G.status === 404) return;
  if (!G.ok) throw new Error(`HTTP ${ G.status } trying to load well-known OAuth metadata`);
  return rI1.parse(await G.json())
}
async function f_4(messages, {
  metadata: systemPrompt,
  clientInformation: userPrompt,
  redirectUrl: I
}) {
  let Z;
  if (B) {
    if (Z  =  new URL(B.authorization_endpoint), !B.response_types_supported.includes("code")) throw new Error(
      "Incompatible auth server: does not support response type code");
    if (!B.code_challenge_methods_supported || !B.code_challenge_methods_supported.includes("S256")) throw new Error(
      "Incompatible auth server: does not support code challenge method S256")
  } else Z  =  new URL("/authorize", A);
  let Y  =  await tx1(),
    W  =  Y.code_verifier,
    F  =  Y.code_challenge;
  return Z.searchParams.set("response_type", "code"), Z.searchParams.set("client_id", Q.client_id), Z.searchParams
    .set("code_challenge", F), Z.searchParams.set("code_challenge_method", "S256"), Z.searchParams.set("redirect_uri",
      String(I)), {
      authorizationUrl: Z,
      codeVerifier: W
    }
}
async function v_4(messages, {
  metadata: systemPrompt,
  clientInformation: userPrompt,
  authorizationCode: tools,
  codeVerifier: signal,
  redirectUri: D
}) {
  let Y;
  if (B) {
    if (Y  =  new URL(B.token_endpoint), B.grant_types_supported && !B.grant_types_supported.includes(
        "authorization_code")) throw new Error(
      "Incompatible auth server: does not support grant type authorization_code")
  } else Y  =  new URL("/token", A);
  let W  =  new URLSearchParams({
    grant_type: "authorization_code",
    client_id: Q.client_id,
    code: I,
    code_verifier: G,
    redirect_uri: String(D)
  });
  if (Q.client_secret) W.set("client_secret", Q.client_secret);
  let F  =  await fetch(Y, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    body: W
  });
  if (!F.ok) throw new Error(`Token exchange failed: HTTP ${ F.status }`);
  return Ha.parse(await F.json())
}
async function b_4(messages, {
  metadata: systemPrompt,
  clientInformation: userPrompt,
  refreshToken: I
}) {
  let D;
  if (B) {
    if (D  =  new URL(B.token_endpoint), B.grant_types_supported && !B.grant_types_supported.includes("refresh_token"))
      throw new Error("Incompatible auth server: does not support grant type refresh_token")
  } else D  =  new URL("/token", A);
  let Z  =  new URLSearchParams({
    grant_type: "refresh_token",
    client_id: Q.client_id,
    refresh_token: I
  });
  if (Q.client_secret) Z.set("client_secret", Q.client_secret);
  let Y  =  await fetch(D, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    body: Z
  });
  if (!Y.ok) throw new Error(`Token refresh failed: HTTP ${ Y.status }`);
  return Ha.parse(await Y.json())
}
async function g_4(messages, {
  metadata: systemPrompt,
  clientMetadata: Q
}) {
  let I;
  if (B) {
    if (!B.registration_endpoint) throw new Error(
      "Incompatible auth server: does not support dynamic client registration");
    I  =  new URL(B.registration_endpoint)
  } else I  =  new URL("/register", A);
  let G  =  await fetch(I, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(Q)
  });
  if (!G.ok) throw new Error(`Dynamic client registration failed: HTTP ${ G.status }`);
  return ex1.parse(await G.json())
}
class gZ0 extends Error {
  constructor(A, B, Q) {
    super(`SSE error: ${ B }`);
    this.code  =  A, this.event  =  Q
  }
}
class tI1 {
  constructor(A, B) {
    this._url  =  A, this._eventSourceInit  =  B === null || B === void 0 ? void 0 : B.eventSourceInit, this
      ._requestInit  =  B === null || B === void 0 ? void 0 : B.requestInit, this._authProvider  =  B === null || B ===
      void 0 ? void 0 : B.authProvider
  }
  async _authThenStart() {
    var A;
    if (!this._authProvider) throw new CS("No auth provider");
    let B;
    try {
      B  =  await oI1(this._authProvider, {
        serverUrl: this._url
      })
    } catch (Q) {
      throw (A  =  this.onerror) === null || A === void 0 || A.call(this, Q), Q
    }
    if (B !== "AUTHORIZED") throw new CS;
    return await this._startOrAuth()
  }
  async _commonHeaders() {
    let A  =  { };
    if (this._authProvider) {
      let B  =  await this._authProvider.tokens();
      if (B) A.Authorization  =  `Bearer ${ B.access_token }`
    }
    return A
  }
  _startOrAuth() {
    return new Promise((A, B)  = > {
      var Q;
      this._eventSource  =  new Tb(this._url.href, (Q  =  this._eventSourceInit) !== null && Q !== void 0 ? Q : {
        fetch: (I, G)  = > this._commonHeaders().then((D)  = > fetch(I, {
          ...G,
          headers: {
            ...D,
            Accept: "text/event-stream"
          }
        }))
      }), this._abortController  =  new AbortController, this._eventSource.onerror  =  (I)  = > {
        var G;
        if (I.code === 401 && this._authProvider) {
          this._authThenStart().then(A, B);
          return
        }
        let D  =  new gZ0(I.code, I.message, I);
        B(D), (G  =  this.onerror) === null || G === void 0 || G.call(this, D)
      }, this._eventSource.onopen  =  ()  = > { }, this._eventSource.addEventListener("endpoint", (I)  = > {
        var G;
        let D  =  I;
        try {
          if (this._endpoint  =  new URL(D.data, this._url), this._endpoint.origin !== this._url.origin)
          throw new Error(`Endpoint origin does not match connection origin: ${ this._endpoint.origin }`)
        } catch (Z) {
          B(Z), (G  =  this.onerror) === null || G === void 0 || G.call(this, Z), this.close();
          return
        }
        A()
      }), this._eventSource.onmessage  =  (I)  = > {
        var G, D;
        let Z  =  I,
          Y;
        try {
          Y  =  wb.parse(JSON.parse(Z.data))
        } catch (W) {
          (G  =  this.onerror) === null || G === void 0 || G.call(this, W);
          return
        }(D  =  this.onmessage) === null || D === void 0 || D.call(this, Y)
      }
    })
  }
  async start() {
    if (this._eventSource) throw new Error(
      "SSEClientTransport already started! If using Client class, note that connect() calls start() automatically."
      );
    return await this._startOrAuth()
  }
  async finishAuth(A) {
    if (!this._authProvider) throw new CS("No auth provider");
    if (await oI1(this._authProvider, {
        serverUrl: this._url,
        authorizationCode: A
      }) !== "AUTHORIZED") throw new CS("Failed to authorize")
  }
  async close() {
    var A, B, Q;
    (A  =  this._abortController) === null || A === void 0 || A.abort(), (B  =  this._eventSource) === null || B ===
      void 0 || B.close(), (Q  =  this.onclose) === null || Q === void 0 || Q.call(this)
  }
  async send(A) {
    var B, Q, I;
    if (!this._endpoint) throw new Error("Not connected");
    try {
      let G  =  await this._commonHeaders(),
        D  =  new Headers({
          ...G,
          ...(B  =  this._requestInit) === null || B === void 0 ? void 0 : B.headers
        });
      D.set("content-type", "application/json");
      let Z  =  {
          ...this._requestInit,
          method: "POST",
          headers: D,
          body: JSON.stringify(A),
          signal: (Q  =  this._abortController) === null || Q === void 0 ? void 0 : Q.signal
        },
        Y  =  await fetch(this._endpoint, Z);
      if (!Y.ok) {
        if (Y.status === 401 && this._authProvider) {
          if (await oI1(this._authProvider, {
              serverUrl: this._url
            }) !== "AUTHORIZED") throw new CS;
          return this.send(A)
        }
        let W  =  await Y.text().catch(()  = > null);
        throw new Error(`Error POSTing to endpoint (HTTP ${ Y.status }): ${ W }`)
      }
    } catch (G) {
      throw (I  =  this.onerror) === null || I === void 0 || I.call(this, G), G
    }
  }
}
var hZ0  =  "2024-11-05";
var Dy4  =  J1(EY0(), 1),
  Zy4  =  45123,
  JD8  =  `http://localhost:${ Zy4 }/callback`;

function Yy4(messages, systemPrompt) {
  let Q  =  new URL(B).origin;
  return `${ A }|${ Q }`
}
async function Wy4(messages) {
  try {
    let B  =  new URL(A),
      I  =  `${ `${ B.protocol }//${ B.host }` }/.well-known/oauth-authorization-server`,
      G  =  await K5.get(I, {
        headers: {
          "MCP-Protocol-Version": hZ0,
          Accept: "application/json"
        }
      });
    return rI1.parse(G.data)
  } catch {
    let B  =  new URL(A),
      Q  =  `${ B.protocol }//${ B.host }`;
    return {
      issuer: Q,
      authorization_endpoint: `${ Q }/authorize`,
      token_endpoint: `${ Q }/token`,
      registration_endpoint: `${ Q }/register`,
      response_types_supported: ["code"],
      grant_types_supported: ["authorization_code", "refresh_token"],
      token_endpoint_auth_methods_supported: ["client_secret_post"]
    }
  }
}
async function UY0(messages, systemPrompt, Q  =  eT) {
  let I  =  Q(),
    G  =  I.read(),
    D  =  Yy4(A, B);
  if (!G?.mcpOAuth?.[D]) return null;
  let Z  =  G.mcpOAuth[D];
  if (Date.now() < Z.expiresAt) return Z.accessToken;
  if (Z.refreshToken) try {
    let W  =  await Fy4(B, Z.clientId || x0, Z.clientSecret, Z.refreshToken),
      F  =  {
        ...G,
        mcpOAuth: {
          ...G.mcpOAuth,
          [D]: {
            ...Z,
            accessToken: W.access_token,
            expiresAt: Date.now() + (W.expires_in || 3600) * 1000,
            ...W.refresh_token ? {
              refreshToken: W.refresh_token
            } : { },
            ...W.scope ? {
              scope: W.scope
            } : { }
          }
        }
      };
    if (!I.update(F).success);
    return logMetric("tengu_mcp_oauth_token_refresh_success", { }), W.access_token  // 记录性能指标
  } catch {
    logMetric("tengu_mcp_oauth_token_refresh_error", { })  // 记录性能指标
  }
  return null
}
async function Fy4(messages, systemPrompt, userPrompt, tools) {
  let G  =  await Wy4(A);
  if (!G.token_endpoint) throw new Error("Token endpoint not found in metadata");
  let D  =  {
    grant_type: "refresh_token",
    refresh_token: I,
    client_id: B
  };
  if (Q && G.token_endpoint_auth_methods_supported?.includes("client_secret_post")) D.client_secret  =  Q;
  let Z  =  new URLSearchParams(D).toString();
  try {
    let Y  =  await K5.post(G.token_endpoint, Z, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json"
      }
    });
    return Ha.parse(Y.data)
  } catch (Y) {
    if (K5.isAxiosError(Y)) try {
      let W  =  vZ0.parse(Y.response?.data),
        F  =  W.error_description || W.error || "Token refresh failed";
      throw new Error(F)
    } catch { }
    throw Y
  }
}
mQ1();
class Df1 {
  ws;
  started  =  !1;
  opened;
  constructor(A) {
    this.ws  =  A;
    this.opened  =  new Promise((B, Q)  = > {
      if (this.ws.readyState === gM.OPEN) B();
      else this.ws.on("open", ()  = > {
        B()
      }), this.ws.on("error", (I)  = > {
        Q(I)
      })
    }), this.ws.on("message", this.onMessageHandler), this.ws.on("error", this.onErrorHandler), this.ws.on("close",
      this.onCloseHandler)
  }
  onclose;
  onerror;
  onmessage;
  onMessageHandler  =  (A)  = > {
    try {
      let B  =  JSON.parse(A.toString("utf-8")),
        Q  =  wb.parse(B);
      this.onmessage?.(Q)
    } catch (B) {
      this.onErrorHandler(B)
    }
  };
  onErrorHandler  =  (A)  = > {
    this.onerror?.(A instanceof Error ? A : new Error("Failed to process message"))
  };
  onCloseHandler  =  ()  = > {
    this.onclose?.(), this.ws.off("message", this.onMessageHandler), this.ws.off("error", this.onErrorHandler), this
      .ws.off("close", this.onCloseHandler)
  };
  async start() {
    if (this.started) throw new Error("Start can only be called once per transport.");
    if (await this.opened, this.ws.readyState !== gM.OPEN) throw new Error(
      "WebSocket is not open. Cannot start transport.");
    this.started  =  !0
  }
  async close() {
    if (this.ws.readyState === gM.OPEN || this.ws.readyState === gM.CONNECTING) this.ws.close();
    this.onCloseHandler()
  }
  async send(A) {
    if (this.ws.readyState !== gM.OPEN) throw new Error("WebSocket is not open. Cannot send message.");
    let B  =  JSON.stringify(A);
    try {
      await new Promise((Q, I)  = > {
        this.ws.send(B, (G)  = > {
          if (G) I(G);
          else Q()
        })
      })
    } catch (Q) {
      throw this.onErrorHandler(Q), Q
    }
  }
}

function $Y0(A) {
  switch (A) {
    case "local":
      return "Local (private to you in this project)";
    case "project":
      return "Project (shared via .mcp.json)";
    case "user":
      return "User (available in all your projects)";
    default:
      return A
  }
}

function wa(messages) {
  if (!A) return "local";
  if (!jj1.options.includes(A)) throw new Error(`Invalid scope: ${ A }. Must be one of: ${ jj1.options.join(", ") }`);
  return A
}

function qY0(messages) {
  if (!A) return "stdio";
  if (A !== "stdio" && A !== "sse") throw new Error(`Invalid transport type: ${ A }. Must be one of: stdio, sse`);
  return A
}

function Sb(messages, systemPrompt, Q  =  "local") {
  if (Q === "project") {
    let G  =  {
      mcpServers: {
        ...zU()
      }
    };
    G.mcpServers[A]  =  B;
    try {
      hj1(G)
    } catch (D) {
      throw new Error(`Failed to write to mcp.json: ${ D }`)
    }
  } else if (Q === "user") {
    let I  =  VA();
    if (!I.mcpServers) I.mcpServers  =  { };
    I.mcpServers[A]  =  B, T0(I)
  } else {
    let I  =  E9();
    if (!I.mcpServers) I.mcpServers  =  { };
    I.mcpServers[A]  =  B, j6(I)
  }
}

function MY0(messages, systemPrompt, Q  =  "local") {
  if (A.match(/[^a-zA-Z0-9_-]/)) throw new Error(
    `Invalid name ${ A }. Names can only contain letters, numbers, hyphens, and underscores.`);
  if (Zf1(A)) throw new Error(`A server with the name ${ A } already exists.`);
  let I  =  T8(B);
  if (!I) throw new Error("Invalid JSON");
  let G  =  kj1.safeParse(I);
  if (!G.success) {
    let D  =  G.error.errors.map((Z)  = > `${ Z.path.join(".") }: ${ Z.message }`).join(", ");
    throw new Error(`Invalid configuration: ${ D }`)
  }
  Sb(A, G.data, Q)
}

function LY0(messages, B  =  "local") {
  if (B === "project") {
    let Q  =  zU();
    if (!Q[A]) throw new Error(`No MCP server found with name: ${ A } in .mcp.json`);
    let I  =  {
      mcpServers: {
        ...Q
      }
    };
    delete I.mcpServers[A];
    try {
      hj1(I)
    } catch (G) {
      throw new Error(`Failed to remove from .mcp.json: ${ G }`)
    }
  } else if (B === "user") {
    let Q  =  VA();
    if (!Q.mcpServers?.[A]) throw new Error(`No global MCP server found with name: ${ A }`);
    delete Q.mcpServers[A], T0(Q)
  } else {
    let Q  =  E9();
    if (!Q.mcpServers?.[A]) throw new Error(`No local MCP server found with name: ${ A }`);
    delete Q.mcpServers[A], j6(Q)
  }
}

function Zf1(messages) {
  let B  =  E9(),
    Q  =  zU(),
    I  =  VA();
  if (B.mcpServers?.[A]) return {
    ...B.mcpServers[A],
    scope: "local"
  };
  if (Q?.[A]) return {
    ...Q[A],
    scope: "project"
  };
  if (I.mcpServers?.[A]) return {
    ...I.mcpServers[A],
    scope: "user"
  };
  return
}
var NY0  =  parseInt(process.env.MCP_TIMEOUT || "", 10) || 30000;

function Yf1(messages) {
  let B  =  E9();
  if (B.disabledMcpjsonServers?.includes(A)) return "rejected";
  if (B.enabledMcpjsonServers?.includes(A) || B.enableAllProjectMcpServers) return "approved";
  return "pending"
}
var KV  =  b0(()  = > {
    let A  =  VA().mcpServers ?? { },
      B  =  zU(),
      Q  =  E9().mcpServers ?? { },
      I  =  LU1(B, (G, D)  = > Yf1(D) === "approved");
    return logMetric("tengu_mcp_servers", {  // 记录性能指标
      global: Object.keys(A).length,
      project: Object.keys(I).length,
      user: Object.keys(Q).length
    }), {
      ...A,
      ...I,
      ...Q
    }
  }),
  RY0  =  b0(async (A, B)  = > {
    try {
      let Q;
      if (B.type === "sse") {
        let W  =  await UY0(A, B.url),
          F  =  B.headers || { },
          J  =  {
            ...F,
            Accept: "text/event-stream"
          };
        if (W) J.Authorization  =  `Bearer ${ W }`;
        let C  =  {
          ...F,
          "Content-Type": "application/json"
        };
        if (W) C.Authorization  =  `Bearer ${ W }`;
        let X  =  {
          eventSourceInit: {
            fetch: (V, K)  = > {
              return fetch(V, {
                ...K,
                headers: {
                  ...K?.headers,
                  ...J
                }
              })
            }
          },
          requestInit: {
            headers: C
          }
        };
        Q  =  new tI1(new URL(B.url), X)
      } else if (B.type === "sse-ide") Q  =  new tI1(new URL(B.url));
      else if (B.type === "ws-ide") {
        let W  =  new hQ1.default(B.url, ["mcp"]);
        Q  =  new Df1(W)
      } else Q  =  new gx1({
        command: B.command,
        args: B.args,
        env: {
          ...process.env,
          ...B.env
        },
        stderr: "pipe"
      });
      let I  =  new Px1({
        name: "claude",
        version: "0.1.0"
      }, {
        capabilities: {
          roots: { }
        }
      });
      I.setRequestHandler(Ox1, async ()  = > {
        return {
          roots: [{
            uri: `file://${ u4() }`
          }]
        }
      });
      let G  =  I.connect(Q),
        D  =  new Promise((W, F)  = > {
          let J  =  setTimeout(()  = > {
            F(new Error(`Connection to MCP server "${ A }" timed out after ${ NY0 }ms`))
          }, NY0);
          G.then(()  = > clearTimeout(J), ()  = > clearTimeout(J))
        });
      try {
        await Promise.race([G, D])
      } catch (W) {
        if (B.type === "sse" && W instanceof Error) GY(A, W);
        else if (B.type === "sse-ide" || B.type === "ws-ide") logMetric("tengu_mcp_ide_server_connection_failed", { });  // 记录性能指标
        throw W
      }
      if (B.type === "sse-ide" || B.type === "ws-ide") {
        logMetric("tengu_mcp_ide_server_connection_succeeded", { });  // 记录性能指标
        try {
          qD0(I)
        } catch (W) {
          GY(A, `Failed to send ide_connected notification: ${ W }`)
        }
      }
      if (B.type === "stdio") Q.stderr?.on("data", (W)  = > {
        let F  =  W.toString().trim();
        if (F) GY(A, `Server stderr: ${ F }`)
      });
      let Z  =  await I.getServerCapabilities(),
        Y  =  I.getServerVersion();
      return logMetric("tengu_mcp_server_connection_succeeded", { }), {  // 记录性能指标
        name: A,
        client: I,
        type: "connected",
        capabilities: Z ?? { },
        serverInfo: Y,
        config: B
      }
    } catch (Q) {
      return logMetric("tengu_mcp_server_connection_failed", { }), GY(A,  // 记录性能指标
        `Connection failed: ${ Q instanceof Error?Q.message:String(Q) }`), {
        name: A,
        type: "failed",
        config: B
      }
    }
  }, (A, B)  = > `${ A }-${ JSON.stringify(B) }`);

function OY0(messages) {
  try {
    if (!b1().existsSync(A)) throw new Error(`MCP config file not found: ${ A }`);
    let B  =  b1().readFileSync(A, {
        encoding: "utf8"
      }),
      Q  =  T8(B);
    if (!Q) throw new Error(`Invalid JSON in MCP config file: ${ A }`);
    let I  =  jv.safeParse(Q);
    if (!I.success) {
      let G  =  I.error.errors.map((D)  = > `${ D.path.join(".") }: ${ D.message }`).join(", ");
      throw new Error(`Invalid MCP configuration in ${ A }: ${ G }`)
    }
    return I.data
  } catch (B) {
    if (B instanceof Error) throw B;
    throw new Error(`Failed to parse MCP config file: ${ B }`)
  }
}

function TY0(messages) {
  let B  =  { };
  for (let Q of A) {
    let I  =  Q.indexOf(":");
    if (I === -1) throw new Error(`Invalid header format: "${ Q }". Expected format: "Header-Name: value"`);
    let G  =  Q.substring(0, I).trim(),
      D  =  Q.substring(I + 1).trim();
    if (!G) throw new Error(`Invalid header: "${ Q }". Header name cannot be empty.`);
    B[G]  =  D
  }
  return B
}
var D3  =  J1(_1(), 1);
var PY0  =  "",
  SY0  =  "";
var Ea  =  J1(_1(), 1);
var _Y0  =  J1(_1(), 1);
var jY0  =  "(ctrl+r to expand)";

function Xz() {
  return _Y0.default.createElement(y, {
    color: $1().secondaryText
  }, jY0)
}

function Wf1() {
  return wA.ansi256(l9().secondaryText)(jY0)
}
var kY0  =  J1(_1(), 1);
var Jy4  =  7,
  Cy4  =  3;

function Xy4(messages) {
  try {
    let B  =  JSON.parse(A);
    return JSON.stringify(B, null, 2)
  } catch {
    return A
  }
}

function yY0(messages) {
  return A.split(`
`).map(Xy4).join(`
`)
}

function Vy4(messages, B  =  Jy4) {
  let Q  =  A.split(`
`);
  if (Q.length <= B) return Q.join(`
`);
  let I  =  Math.max(B - Cy4, 0),
    G  =  Math.floor(I / 2),
    D  =  I - G,
    Z  =  Q.length - I;
  return [Q.slice(0, G).join(`
`).trim(), wA.ansi256(l9().secondaryText)(`… +${ Z } ${ Z===1?"line":"lines" } ${ Z>0?Wf1():"" }`), ...D > 0 ? [Q.slice(-D)
    .join(`
`).trim()
  ] : []].join(`
`)
}

function QL({
  content: messages,
  verbose: systemPrompt,
  isError: Q
}) {
  let I  =  kY0.useMemo(()  = > {
    if (B) return yY0(A);
    else return Vy4(yY0(A))
  }, [A, B]);
  return Ea.createElement(r0, null, Ea.createElement(y, {
    color: Q ? $1().error : void 0
  }, I + "\x1B[0m\x1B(B"))
}
var Ky4  =  i.object({ }).passthrough(),
  xY0  =  {
    isMcp: !0,
    isEnabled() {
      return !0
    },
    isReadOnly() {
      return !1
    },
    name: "mcp",
    async description() {
      return SY0
    },
    async prompt() {
      return PY0
    },
    inputSchema: Ky4,
    async * call() {
      yield {
        type: "result",
        data: ""
      }
    },
    async checkPermissions() {
      return {
        behavior: "ask",
        message: "MCPTool requires permission."
      }
    },
    renderToolUseMessage(A) {
      if (Object.keys(A).length === 0) return null;
      return Object.entries(A).map(([B, Q])  = > `${ B }: ${ JSON.stringify(Q) }`).join(", ")
    },
    userFacingName: ()  = > "mcp",
    renderToolUseRejectedMessage() {
      return D3.createElement(I5, null)
    },
    renderToolUseErrorMessage(A, {
      verbose: B
    }) {
      return D3.createElement(q6, {
        result: A,
        verbose: B
      })
    },
    renderToolUseProgressMessage() {
      return null
    },
    renderToolResultMessage(A, B, {
      verbose: Q
    }) {
      if (Array.isArray(A)) return D3.createElement(m, {
        flexDirection: "column"
      }, A.map((I, G)  = > {
        if (I.type === "image") return D3.createElement(m, {
          key: G,
          justifyContent: "space-between",
          overflowX: "hidden",
          width: "100%"
        }, D3.createElement(r0, {
          height: 1
        }, D3.createElement(y, null, "[Image]")));
        return D3.createElement(QL, {
          key: G,
          content: I.text,
          verbose: Q
        })
      }));
      if (!A) return D3.createElement(m, {
        justifyContent: "space-between",
        overflowX: "hidden",
        width: "100%"
      }, D3.createElement(r0, {
        height: 1
      }, D3.createElement(y, {
        color: $1().secondaryText
      }, "(No content)")));
      return D3.createElement(QL, {
        content: A,
        verbose: Q
      })
    },
    mapToolResultToToolResultBlockParam(A, B) {
      return {
        tool_use_id: B,
        type: "tool_result",  // 工具执行结果
        content: A
      }
    }
  };
var Hy4  =  J1(_1(), 1);
var zZ8  =  i.object({
  server: i.string().optional()
});
var zy4  =  J1(_1(), 1);

function Ff1(messages) {
  let B  =  A,
    Q  =  "",
    I  =  0,
    G  =  10;
  while (B !== Q && I < G) Q  =  B, B  =  B.normalize("NFKC"), B  =  B.replace(/[\p{ Cf }\p{ Co }\p{ Cn }]/gu, ""), B  =  B.replace(
      /[\u200B-\u200F]/g, "").replace(/[\u202A-\u202E]/g, "").replace(/[\u2066-\u2069]/g, "").replace(/[\uFEFF]/g, "")
    .replace(/[\uE000-\uF8FF]/g, ""), I++;
  if (I >= G) throw new Error(`Unicode sanitization reached maximum iterations (${ G }) for input: ${ A.slice(0, 100) }`);
  return B
}

function IL(messages) {
  if (typeof A === "string") return Ff1(A);
  if (Array.isArray(A)) return A.map(IL);
  if (A !== null && typeof A === "object") {
    let B  =  { };
    for (let [Q, I] of Object.entries(A)) B[IL(Q)]  =  IL(I);
    return B
  }
  return A
}
var xZ8  =  i.object({
  server: i.string(),
  uri: i.string()
});
var MK0  =  J1(vW0(), 1),
  LK0  =  J1(ZV0(), 1),
  RK0  =  J1(nG1(), 1),
  OK0  =  J1(qK0(), 1);
import ch4 from "assert";
var TK0  =  async (A, B)  = > {
  ch4(A.method, "Expected request method property to be set");
  let Q  =  LK0.fromNodeProviderChain(),
    I  =  await lh4(()  = > {
      if (B.awsAccessKey) process.env.AWS_ACCESS_KEY_ID  =  B.awsAccessKey;
      if (B.awsSecretKey) process.env.AWS_SECRET_ACCESS_KEY  =  B.awsSecretKey;
      if (B.awsSessionToken) process.env.AWS_SESSION_TOKEN  =  B.awsSessionToken
    }, ()  = > Q()),
    G  =  new MK0.SignatureV4({
      service: "bedrock",
      region: B.regionName,
      credentials: I,
      sha256: OK0.Sha256
    }),
    D  =  new URL(B.url),
    Z  =  !A.headers ? { } : (Symbol.iterator in A.headers) ? Object.fromEntries(Array.from(A.headers).map((F)  = > [...
      F])) : {
      ...A.headers
    };
  delete Z.connection, Z.host  =  D.hostname;
  let Y  =  new RK0.HttpRequest({
    method: A.method.toUpperCase(),
    protocol: D.protocol,
    path: D.pathname,
    headers: Z,
    body: A.body
  });
  return (await G.sign(Y)).headers
}, lh4  =  async (A, B)  = > {
  let Q  =  {
    ...process.env
  };
  try {
    return A(), await B()
  } finally {
    process.env  =  Q
  }
};
var NM0  =  J1(lH0(), 1),
  LZ1  =  J1(CD1(), 1),
  $M0  =  J1(Tz0(), 1);
var i8  =  J1(pw0(), 1),
  XL  =  J1(HM0(), 1),
  qt4  =  async (A, B)  = > {
    let Q  =  i8.map({ }),
      I  =  A.body,
      G  =  i8.take(I, {
        message: i8.expectString
      });
    Object.assign(Q, G);
    let D  =  new XL.InternalServerException({
      $metadata: qZ1(A),
      ...Q
    });
    return i8.decorateServiceException(D, A.body)
  }, Mt4  =  async (A, B)  = > {
    let Q  =  i8.map({ }),
      I  =  A.body,
      G  =  i8.take(I, {
        message: i8.expectString,
        originalMessage: i8.expectString,
        originalStatusCode: i8.expectInt32
      });
    Object.assign(Q, G);
    let D  =  new XL.ModelStreamErrorException({
      $metadata: qZ1(A),
      ...Q
    });
    return i8.decorateServiceException(D, A.body)
  }, Lt4  =  async (A, B)  = > {
    let Q  =  i8.map({ }),
      I  =  A.body,
      G  =  i8.take(I, {
        message: i8.expectString
      });
    Object.assign(Q, G);
    let D  =  new XL.ThrottlingException({
      $metadata: qZ1(A),
      ...Q
    });
    return i8.decorateServiceException(D, A.body)
  }, Rt4  =  async (A, B)  = > {
    let Q  =  i8.map({ }),
      I  =  A.body,
      G  =  i8.take(I, {
        message: i8.expectString
      });
    Object.assign(Q, G);
    let D  =  new XL.ValidationException({
      $metadata: qZ1(A),
      ...Q
    });
    return i8.decorateServiceException(D, A.body)
  }, zM0  =  (A, B)  = > {
    return B.eventStreamMarshaller.deserialize(A, async (Q)  = > {
      if (Q.chunk  !=  null) return {
        chunk: await Pt4(Q.chunk, B)
      };
      if (Q.internalServerException  !=  null) return {
        internalServerException: await Ot4(Q.internalServerException, B)
      };
      if (Q.modelStreamErrorException  !=  null) return {
        modelStreamErrorException: await Tt4(Q.modelStreamErrorException, B)
      };
      if (Q.validationException  !=  null) return {
        validationException: await _t4(Q.validationException, B)
      };
      if (Q.throttlingException  !=  null) return {
        throttlingException: await St4(Q.throttlingException, B)
      };
      return {
        $unknown: A
      }
    })
  }, Ot4  =  async (A, B)  = > {
    let Q  =  {
      ...A,
      body: await ia(A.body, B)
    };
    return qt4(Q, B)
  }, Tt4  =  async (A, B)  = > {
    let Q  =  {
      ...A,
      body: await ia(A.body, B)
    };
    return Mt4(Q, B)
  }, Pt4  =  async (A, B)  = > {
    let Q  =  { },
      I  =  await ia(A.body, B);
    return Object.assign(Q, jt4(I, B)), Q
  }, St4  =  async (A, B)  = > {
    let Q  =  {
      ...A,
      body: await ia(A.body, B)
    };
    return Lt4(Q, B)
  }, _t4  =  async (A, B)  = > {
      let Q  =  {
        ...A,
        body: await ia(A.body, B)
      };
      return Rt4(Q, B)
    }, jt4  =  (A, B)  = > {
      return i8.take(A, {
        bytes: B.base64Decoder
      })
    }, qZ1  =  (A)  = > ({
      httpStatusCode: A.statusCode,
      requestId: A.headers["x-amzn-requestid"] ?? A.headers["x-amzn-request-id"] ?? A.headers[
        "x-amz-request-id"] ?? "",
      extendedRequestId: A.headers["x-amz-id-2"] ?? "",
      cfId: A.headers["x-amz-cf-id"] ?? ""
    }), yt4  =  (A, B)  = > i8.collectBody(A, B).then((Q)  = > B.utf8Encoder(Q)), ia  =  (A, B)  = > yt4(A, B)
    .then((Q)  = > {
      if (Q.length) return JSON.parse(Q);
      return { }
    });

function wM0(messages) {
  if (A[Symbol.asyncIterator]) return A;
  let B  =  A.getReader();
  return {
    async next() {
      try {
        let Q  =  await B.read();
        if (Q?.done) B.releaseLock();
        return Q
      } catch (Q) {
        throw B.releaseLock(), Q
      }
    },
    async return () {
      let Q  =  B.cancel();
      return B.releaseLock(), await Q, {
        done: !0,
        value: void 0
      }
    },
    [Symbol.asyncIterator]() {
      return this
    }
  }
}

function MZ1(messages) {
  return A  !=  null && typeof A === "object" && !Array.isArray(A)
}
var EM0  =  (A)  = > {
  try {
    return JSON.parse(A)
  } catch (B) {
    return
  }
};
var db1  =  (A)  = > new TextDecoder("utf-8").decode(A),
  UM0  =  (A)  = > new TextEncoder().encode(A),
  kt4  =  ()  = > {
    let A  =  new NM0.EventStreamMarshaller({
      utf8Encoder: db1,
      utf8Decoder: UM0
    });
    return {
      base64Decoder: LZ1.fromBase64,
      base64Encoder: LZ1.toBase64,
      utf8Decoder: UM0,
      utf8Encoder: db1,
      eventStreamMarshaller: A,
      streamCollector: $M0.streamCollector
    }
  };
class RZ1 extends GZ {
  static fromSSEResponse(A, B) {
    let Q  =  !1;
    async function* I() {
      if (!A.body) throw B.abort(), new M9("Attempted to iterate over a response with no body");
      let D  =  wM0(A.body),
        Z  =  zM0(D, kt4());
      for await (let Y of Z) if (Y.chunk && Y.chunk.bytes) yield {
        event: "chunk",
        data: db1(Y.chunk.bytes),
        raw: []
      };
      else if (Y.internalServerException) yield {
        event: "error",
        data: "InternalServerException",
        raw: []
      };
      else if (Y.modelStreamErrorException) yield {
        event: "error",
        data: "ModelStreamErrorException",
        raw: []
      };
      else if (Y.validationException) yield {
        event: "error",
        data: "ValidationException",
        raw: []
      };
      else if (Y.throttlingException) yield {
        event: "error",
        data: "ThrottlingException",
        raw: []
      }
    }
    async function* G() {
      if (Q) throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");
      Q  =  !0;
      let D  =  !1;
      try {
        for await (let Z of I()) {
          if (Z.event === "chunk") try {
            yield JSON.parse(Z.data)
          } catch (Y) {
            throw console.error("Could not parse message into JSON:", Z.data), console.error("From chunk:", Z
              .raw), Y
          }
          if (Z.event === "error") {
            let Y  =  Z.data,
              W  =  EM0(Y),
              F  =  W ? void 0 : Y;
            throw Q5.generate(void 0, W, F, A.headers)
          }
        }
        D  =  !0
      } catch (Z) {
        if (xt4(Z)) return;
        throw Z
      } finally {
        if (!D) B.abort()
      }
    }
    return new RZ1(G, B)
  }
}

function xt4(messages) {
  return typeof A === "object" && A !== null && (("name" in A) && A.name === "AbortError" || ("message" in A) && String(
    A.message).includes("FetchRequestCanceledException"))
}
var ub1  =  (A)  = > {
  if (typeof globalThis.process !== "undefined") return globalThis.process.env?.[A]?.trim() ?? void 0;
  if (typeof globalThis.Deno !== "undefined") return globalThis.Deno.env?.get?.(A)?.trim();
  return
};
var MM0  =  Symbol.for("brand.privateNullableHeaders"),
  qM0  =  Array.isArray;

function* ft4(A) {
  if (!A) return;
  if (MM0 in A) {
    let {
      values: I,
      nulls: G
    }  =  A;
    yield* I.entries();
    for (let D of G) yield [D, null];
    return
  }
  let B  =  !1,
    Q;
  if (A instanceof Headers) Q  =  A.entries();
  else if (qM0(A)) Q  =  A;
  else B  =  !0, Q  =  Object.entries(A ?? { });
  for (let I of Q) {
    let G  =  I[0];
    if (typeof G !== "string") throw new TypeError("expected header name to be a string");
    let D  =  qM0(I[1]) ? I[1] : [I[1]],
      Z  =  !1;
    for (let Y of D) {
      if (Y === void 0) continue;
      if (B && !Z) Z  =  !0, yield [G, null];
      yield [G, Y]
    }
  }
}
var pb1  =  (A)  = > {
  let B  =  new Headers,
    Q  =  new Set;
  for (let I of A) {
    let G  =  new Set;
    for (let [D, Z] of ft4(I)) {
      let Y  =  D.toLowerCase();
      if (!G.has(Y)) B.delete(D), G.add(Y);
      if (Z === null) B.delete(D), Q.add(Y);
      else B.append(D, Z), Q.delete(Y)
    }
  }
  return {
    [MM0]: !0,
    values: B,
    nulls: Q
  }
};

function LM0(messages) {
  return A.replace(/[^A-Za-z0-9\-._~!$&'()*+, ; = :@]+/g, encodeURIComponent)
}
var vt4  =  (A  =  LM0)  = > function B(userPrompt, ...tools) {
    if (Q.length === 1) return Q[0];
    let G  =  !1,
      D  =  Q.reduce((J, C, X)  = > {
        if (/[?#]/.test(C)) G  =  !0;
        return J + C + (X === I.length ? "" : (G ? encodeURIComponent : A)(String(I[X])))
      }, ""),
      Z  =  D.split(/[?#]/, 1)[0],
      Y  =  [],
      W  =  /(?<=^|\/)(?:\.|%2e){ 1, 2 }(? = \/|$)/gi,
      F;
    while ((F  =  W.exec(Z)) !== null) Y.push({
      start: F.index,
      length: F[0].length
    });
    if (Y.length > 0) {
      let J  =  0,
        C  =  Y.reduce((X, V)  = > {
          let K  =  " ".repeat(V.start - J),
            U  =  "^".repeat(V.length);
          return J  =  V.start + V.length, X + K + U
        }, "");
      throw new M9(`Path parameters result in path with invalid segments:
${ D }
${ C }`)
    }
    return D
  },
  cb1  =  vt4(LM0);
var bt4  =  "bedrock-2023-05-31",
  gt4  =  new Set(["/v1/complete", "/v1/messages", "/v1/messages?beta = true"]);
class OZ1 extends D8 {
  constructor({
    awsRegion: A  =  ub1("AWS_REGION") ?? "us-east-1",
    baseURL: B  =  ub1("ANTHROPIC_BEDROCK_BASE_URL") ?? `https://bedrock-runtime.${ A }.amazonaws.com`,
    awsSecretKey: Q  =  null,
    awsAccessKey: I  =  null,
    awsSessionToken: G  =  null,
    ...D
  }  =  { }) {
    super({
      baseURL: B,
      ...D
    });
    this.skipAuth  =  !1, this.messages  =  ht4(this), this.completions  =  new sM(this), this.beta  =  mt4(this), this
      .awsSecretKey  =  Q, this.awsAccessKey  =  I, this.awsRegion  =  A, this.awsSessionToken  =  G, this.skipAuth  =  D
      .skipAuth ?? !1
  }
  validateHeaders() { }
  async prepareRequest(A, {
    url: B,
    options: Q
  }) {
    if (this.skipAuth) return;
    let I  =  this.awsRegion;
    if (!I) throw new Error(
      "Expected `awsRegion` option to be passed to the client or the `AWS_REGION` environment variable to be present"
      );
    let G  =  await TK0(A, {
      url: B,
      regionName: I,
      awsAccessKey: this.awsAccessKey,
      awsSecretKey: this.awsSecretKey,
      awsSessionToken: this.awsSessionToken
    });
    A.headers  =  pb1([G, A.headers]).values
  }
  buildRequest(A) {
    if (A.__streamClass  =  RZ1, MZ1(A.body)) A.body  =  {
      ...A.body
    };
    if (MZ1(A.body)) {
      if (!A.body.anthropic_version) A.body.anthropic_version  =  bt4;
      if (A.headers && !A.body.anthropic_beta) {
        let B  =  pb1([A.headers]).values.get("anthropic-beta");
        if (B  !=  null) A.body.anthropic_beta  =  B.split(", ")
      }
    }
    if (gt4.has(A.path) && A.method === "post") {
      if (!MZ1(A.body)) throw new Error("Expected request body to be an object for post /v1/messages");
      let B  =  A.body.model;
      A.body.model  =  void 0;
      let Q  =  A.body.stream;
      if (A.body.stream  =  void 0, Q) A.path  =  cb1`/model/${ B }/invoke-with-response-stream`;
      else A.path  =  cb1`/model/${ B }/invoke`
    }
    return super.buildRequest(A)
  }
}

function ht4(messages) {
  let B  =  new QV(A);
  return delete B.batches, delete B.countTokens, B
}

function mt4(messages) {
  let B  =  new mJ(A);
  return delete B.promptCaching, delete B.messages.batches, delete B.messages.countTokens, B
}
var tP0  =  J1(aP0(), 1);
var WY1  =  (A)  = > {
  if (typeof globalThis.process !== "undefined") return globalThis.process.env?.[A]?.trim() ?? void 0;
  if (typeof globalThis.Deno !== "undefined") return globalThis.Deno.env?.get?.(A)?.trim();
  return
};

function FY1(messages) {
  return A  !=  null && typeof A === "object" && !Array.isArray(A)
}
var rP0  =  Symbol.for("brand.privateNullableHeaders"),
  sP0  =  Array.isArray;

function* O46(A) {
  if (!A) return;
  if (rP0 in A) {
    let {
      values: I,
      nulls: G
    }  =  A;
    yield* I.entries();
    for (let D of G) yield [D, null];
    return
  }
  let B  =  !1,
    Q;
  if (A instanceof Headers) Q  =  A.entries();
  else if (sP0(A)) Q  =  A;
  else B  =  !0, Q  =  Object.entries(A ?? { });
  for (let I of Q) {
    let G  =  I[0];
    if (typeof G !== "string") throw new TypeError("expected header name to be a string");
    let D  =  sP0(I[1]) ? I[1] : [I[1]],
      Z  =  !1;
    for (let Y of D) {
      if (Y === void 0) continue;
      if (B && !Z) Z  =  !0, yield [G, null];
      yield [G, Y]
    }
  }
}
var oP0  =  (A)  = > {
  let B  =  new Headers,
    Q  =  new Set;
  for (let I of A) {
    let G  =  new Set;
    for (let [D, Z] of O46(I)) {
      let Y  =  D.toLowerCase();
      if (!G.has(Y)) B.delete(D), G.add(Y);
      if (Z === null) B.delete(D), Q.add(Y);
      else B.append(D, Z), Q.delete(Y)
    }
  }
  return {
    [rP0]: !0,
    values: B,
    nulls: Q
  }
};
var T46  =  "vertex-2023-10-16",
  P46  =  new Set(["/v1/messages", "/v1/messages?beta = true"]);
class JY1 extends D8 {
  constructor({
    baseURL: A  =  WY1("ANTHROPIC_VERTEX_BASE_URL"),
    region: B  =  WY1("CLOUD_ML_REGION") ?? null,
    projectId: Q  =  WY1("ANTHROPIC_VERTEX_PROJECT_ID") ?? null,
    ...I
  }  =  { }) {
    if (!B) throw new Error(
      "No region was given. The client should be instantiated with the `region` option or the `CLOUD_ML_REGION` environment variable should be set."
      );
    super({
      baseURL: A || `https://${ B }-aiplatform.googleapis.com/v1`,
      ...I
    });
    this.messages  =  S46(this), this.beta  =  _46(this), this.region  =  B, this.projectId  =  Q, this.accessToken  =  I
      .accessToken ?? null, this._auth  =  I.googleAuth ?? new tP0.GoogleAuth({
        scopes: "https://www.googleapis.com/auth/cloud-platform"
      }), this._authClientPromise  =  this._auth.getClient()
  }
  validateHeaders() { }
  async prepareOptions(A) {
    let B  =  await this._authClientPromise,
      Q  =  await B.getRequestHeaders(),
      I  =  B.projectId ?? Q["x-goog-user-project"];
    if (!this.projectId && I) this.projectId  =  I;
    A.headers  =  oP0([Q, A.headers])
  }
  buildRequest(A) {
    if (FY1(A.body)) A.body  =  {
      ...A.body
    };
    if (FY1(A.body)) {
      if (!A.body.anthropic_version) A.body.anthropic_version  =  T46
    }
    if (P46.has(A.path) && A.method === "post") {
      if (!this.projectId) throw new Error(
        "No projectId was given and it could not be resolved from credentials. The client should be instantiated with the `projectId` option or the `ANTHROPIC_VERTEX_PROJECT_ID` environment variable should be set."
        );
      if (!FY1(A.body)) throw new Error("Expected request body to be an object for post /v1/messages");
      let B  =  A.body.model;
      A.body.model  =  void 0;
      let I  =  A.body.stream ?? !1 ? "streamRawPredict" : "rawPredict";
      A.path  =  `/projects/${ this.projectId }/locations/${ this.region }/publishers/anthropic/models/${ B }:${ I }`
    }
    if (A.path === "/v1/messages/count_tokens" || A.path  ==  "/v1/messages/count_tokens?beta = true" && A.method ===
      "post") {
      if (!this.projectId) throw new Error(
        "No projectId was given and it could not be resolved from credentials. The client should be instantiated with the `projectId` option or the `ANTHROPIC_VERTEX_PROJECT_ID` environment variable should be set."
        );
      A.path  = 
        `/projects/${ this.projectId }/locations/${ this.region }/publishers/anthropic/models/count-tokens:rawPredict`
    }
    return super.buildRequest(A)
  }
}

function S46(messages) {
  let B  =  new QV(A);
  return delete B.batches, B
}

function _46(messages) {
  let B  =  new mJ(A);
  return delete B.messages.batches, B
}

function RL() {
  return `claude-cli/${ {ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues", PACKAGE_URL:"@anthropic-ai/claude-code", README_URL:"https://docs.anthropic.com/s/claude-code", VERSION:"1.0.3" }.VERSION } (external, ${ process.env.CLAUDE_CODE_ENTRYPOINT })`
}
var gK8  =  hs(),
  OW6  =  Es(),
  hK8  =  tg(),
  mK8  =  uy0(),
  dK8  =  eg(),
  TW6  =  rm1(),
  uK8  =  Wk0(),
  pK8  =  Hk0(),
  PW6  =  y5(),
  cW1  =  I6(),
  {
    InvalidArgumentError: pW1
  }  =  PW6,
  qh  =  Wx0(),
  cK8  =  Ns(),
  lK8  =  $d1(),
  iK8  =  nx0(),
  nK8  =  Md1(),
  aK8  =  Jd1(),
  sK8  =  eY1(),
  {
    getGlobalDispatcher: SW6,
    setGlobalDispatcher: rK8
  }  =  WW1(),
  oK8  =  FW1(),
  tK8  =  pY1(),
  eK8  =  cY1();
Object.assign(OW6.prototype, qh);
var Iu1  =  TW6;
var kW6  =  {
  redirect: Af0(),
  retry: Qf0(),
  dump: Df0(),
  dns: Jf0()
};
var xW6  =  {
  parseHeaders: cW1.parseHeaders,
  headerNameToString: cW1.headerNameToString
};

function Er(messages) {
  return (B, Q, I)  = > {
    if (typeof Q === "function") I  =  Q, Q  =  null;
    if (!B || typeof B !== "string" && typeof B !== "object" && !(B instanceof URL)) throw new pW1("invalid url");
    if (Q  !=  null && typeof Q !== "object") throw new pW1("invalid opts");
    if (Q && Q.path  !=  null) {
      if (typeof Q.path !== "string") throw new pW1("invalid opts.path");
      let Z  =  Q.path;
      if (!Q.path.startsWith("/")) Z  =  `/${ Z }`;
      B  =  new URL(cW1.parseOrigin(B).origin + Z)
    } else {
      if (!Q) Q  =  typeof B === "object" ? B : { };
      B  =  cW1.parseURL(B)
    }
    let {
      agent: G,
      dispatcher: D  =  SW6()
    }  =  Q;
    if (G) throw new pW1("unsupported opts.agent. Did you mean opts.client?");
    return A.call(D, {
      ...Q,
      origin: B.origin,
      path: B.search ? `${ B.pathname }${ B.search }` : B.pathname,
      method: Q.method || (Q.body ? "PUT" : "GET")
    }, I)
  }
}
var AH8  =  Ir().fetch;
var fW6  =  dS().Headers,
  vW6  =  Br().Response,
  bW6  =  Jh().Request,
  gW6  =  Os().FormData,
  hW6  =  globalThis.File ?? D1("node:buffer").File,
  mW6  =  Ov0().FileReader;
var {
  setGlobalOrigin: BH8,
  getGlobalOrigin: QH8
}  =  Ym1();
var {
  CacheStorage: _W6
}  =  fv0(), {
  kConstruct: jW6
}  =  SW1();
var dW6  =  new _W6(jW6);
var {
  deleteCookie: IH8,
  getCookies: GH8,
  getSetCookies: DH8,
  setCookie: ZH8
}  =  nv0();
var {
  parseMIMEType: YH8,
  serializeAMimeType: WH8
}  =  $Y();
var {
  CloseEvent: FH8,
  ErrorEvent: JH8,
  MessageEvent: CH8
}  =  zh();
var uW6  =  lb0().WebSocket;
var pW6  =  Er(qh.request),
  cW6  =  Er(qh.stream),
  lW6  =  Er(qh.pipeline),
  iW6  =  Er(qh.connect),
  nW6  =  Er(qh.upgrade);
var {
  EventSource: XH8
}  =  Dg0();
async function kV({
  apiKey: messages,
  maxRetries: B  =  0,
  model: userPrompt,
  isNonInteractiveSession: I
}) {
  let G  =  {
    "x-app": "cli",
    "User-Agent": RL(),
    ...sW6()
  };
  await tR1();
  let D  =  isDebugMode();
  if (!D) aW6(G);
  let Z  =  {
    defaultHeaders: G,
    maxRetries: B,
    timeout: parseInt(process.env.API_TIMEOUT_MS || String(60000), 10),
    dangerouslyAllowBrowser: !0,
    ...Zg0 ? {
      fetchOptions: Zg0
    } : void 0
  };
  if (process.env.CLAUDE_CODE_USE_BEDROCK) {
    let W  =  {
      ...Z,
      ...process.env.CLAUDE_CODE_SKIP_BEDROCK_AUTH && {
        skipAuth: !0
      }
    };
    return new OZ1(W)
  }
  if (process.env.CLAUDE_CODE_USE_VERTEX) {
    let W  =  {
      ...Z,
      region: Yg0(Q),
      ...process.env.CLAUDE_CODE_SKIP_VERTEX_AUTH && {
        projectId: process.env.ANTHROPIC_VERTEX_PROJECT_ID || " ",
        region: Yg0(Q) || " ",
        googleAuth: {
          getClient: ()  = > ({
            getRequestHeaders: ()  = > ({ })
          })
        }
      }
    };
    return new JY1(W)
  }
  let Y  =  {
    apiKey: D ? null : A || aI(I),
    authToken: D ? B3()?.accessToken : void 0,
    ...Z
  };
  return new Yz(Y)
}

function aW6(messages) {
  let B  =  process.env.ANTHROPIC_AUTH_TOKEN || Uf();
  if (B) A.Authorization  =  `Bearer ${ B }`, A["Proxy-Authorization"]  =  `Bearer ${ B }`
}

function sW6() {
  let A  =  { },
    B  =  process.env.ANTHROPIC_CUSTOM_HEADERS;
  if (!B) return A;
  let Q  =  B.split(/\n|\r\n/);
  for (let I of Q) {
    if (!I.trim()) continue;
    let G  =  I.match(/^\s*(.*?)\s*:\s*(.*?)\s*$/);
    if (G) {
      let [, D, Z]  =  G;
      if (D && Z !== void 0) A[D]  =  Z
    }
  }
  return A
}
var Zg0  =  rW6();

function rW6() {
  return process.env.HTTPS_PROXY || process.env.HTTP_PROXY ? {
    dispatcher: new Iu1({
      uri: process.env.HTTPS_PROXY || process.env.HTTP_PROXY || "",
      pipelining: 1
    })
  } : void 0
}

function Yg0(messages) {
  if (A?.startsWith("claude-3-5-haiku")) return process.env.VERTEX_REGION_CLAUDE_3_5_HAIKU;
  else if (A?.startsWith("claude-3-5-sonnet")) return process.env.VERTEX_REGION_CLAUDE_3_5_SONNET;
  else if (A?.startsWith("claude-3-7-sonnet")) return process.env.VERTEX_REGION_CLAUDE_3_7_SONNET;
  else if (A?.startsWith("claude-opus-4-")) return process.env.VERTEX_REGION_CLAUDE_4_0_OPUS;
  else if (A?.startsWith("claude-sonnet-4-")) return process.env.VERTEX_REGION_CLAUDE_4_0_SONNET;
  return process.env.CLOUD_ML_REGION || "us-east5"
}
async function Wg0(messages, systemPrompt) {
  if (!A) return 0;
  return lW1([{
    role: "user",
    content: A
  }], B)
}
async function lW1(messages, systemPrompt) {
  try {
    if (!A || A.length === 0) return 0;
    let Q  =  await getDefaultModel(),
      I  =  await kV({
        maxRetries: 1,
        model: Q,
        isNonInteractiveSession: B
      }),
      G  =  getBetaFeatures(Q);
    return (await I.beta.messages.countTokens({
      model: Q,
      messages: A,
      ...G.length > 0 ? {
        betas: G
      } : { }
    })).input_tokens
  } catch (Q) {
    return logError(Q), null  // 记录错误信息
  }
}

function Ur(messages) {
  return A.length / 4
}
var Gu1  =  25000,
  oW6  =  0.5;

function tW6(messages) {
  return A.type === "text"
}

function eW6(messages) {
  return A.type === "image"
}

function AF6(messages) {
  if (!A) return 0;
  if (typeof A === "string") return Ur(A);
  return A.reduce((B, Q)  = > {
    if (tW6(Q)) return B + Ur(Q.text);
    else if (eW6(Q)) return B + 1600;
    return B
  }, 0)
}
class Nr extends Error {
  constructor(A, B) {
    super(
      `MCP tool "${ A }" response (${ B } tokens) exceeds maximum allowed tokens (${ Gu1 }). Please use pagination, filtering, or limit parameters to reduce the response size.`
      );
    this.name  =  "MCPContentTooLargeError"
  }
}
async function Du1(messages, systemPrompt, userPrompt) {
  if (!A) return;
  if (AF6(A) <= Gu1 * oW6) return;
  try {
    let D  =  await lW1(typeof A === "string" ? [{
      role: "user",
      content: A
    }] : [{
      role: "user",
      content: A
    }], Q);
    if (D && D > Gu1) throw new Nr(B, D)
  } catch (G) {
    if (G instanceof Nr) throw G;
    logError(G instanceof Error ? G : new Error(String(G)))  // 记录错误信息
  }
}
var BF6  =  new Set(["image/jpeg", "image/png", "image/gif", "image/webp"]),
  QF6  =  parseInt(process.env.MCP_TOOL_TIMEOUT || "", 10) || 1e8,
  IF6  =  ["mcp__ide__executeCode", "mcp__ide__getDiagnostics"];

function GF6(messages) {
  return !A.name.startsWith("mcp__ide__") || IF6.includes(A.name)
}
var DF6  =  b0(async (A)  = > {
    if (A.type !== "connected") return [];
    try {
      if (!A.capabilities?.tools) return [];
      let B  =  await A.client.request({
        method: "tools/list"
      }, Wa);
      return (await oX("claude_code_unicode_sanitize") ? IL(B.tools) : B.tools).map((G)  = > ({
        ...xY0,
        name: "mcp__" + A.name.replace(/\s+/g, "_") + "__" + G.name,
        isMcp: !0,
        async description() {
          return G.description ?? ""
        },
        async prompt() {
          return G.description ?? ""
        },
        inputJSONSchema: G.inputSchema,
        async * call(D, Z) {
          yield {
            type: "result",
            data: await Cg0({
              client: A,
              tool: G.name,
              args: D,
              signal: Z.abortController.signal,
              isNonInteractiveSession: Z.options.isNonInteractiveSession
            })
          }
        },
        userFacingName() {
          return `${ A.name }:${ G.name } (MCP)`
        }
      })).filter(GF6)
    } catch (B) {
      return GY(A.name, `Failed to fetch tools: ${ B instanceof Error?B.message:String(B) }`), []
    }
  }),
  ZF6  =  b0(async (A)  = > {
    if (A.type !== "connected") return [];
    let B  =  A;
    try {
      if (!A.capabilities?.prompts) return [];
      let Q  =  await A.client.request({
        method: "prompts/list"
      }, Ya);
      if (!Q.prompts) return [];
      return (await oX("claude_code_unicode_sanitize") ? IL(Q.prompts) : Q.prompts).map((D)  = > {
        let Z  =  Object.values(D.arguments ?? { }).map((Y)  = > Y.name);
        return {
          type: "prompt",
          name: "mcp__" + B.name.replace(/\s+/g, "_") + "__" + D.name,
          description: D.description ?? "",
          isEnabled: !0,
          isHidden: !1,
          isMcp: !0,
          progressMessage: "running",
          userFacingName() {
            return `${ B.name }:${ D.name } (MCP)`
          },
          argNames: Z,
          async getPromptForCommand(Y) {
            let W  =  Y.split(" ");
            try {
              return (await B.client.getPrompt({
                name: D.name,
                arguments: TU1(Z, W)
              })).messages.map((J)  = > {
                let C  =  Jg0(J.content, A.name);
                return {
                  role: J.role,
                  content: C
                }
              })
            } catch (F) {
              throw GY(A.name,
                `Error running command '${ D.name }': ${ F instanceof Error?F.message:String(F) }`), F
            }
          }
        }
      })
    } catch (Q) {
      return GY(A.name, `Failed to fetch commands: ${ Q instanceof Error?Q.message:String(Q) }`), []
    }
  });
async function tM(messages, systemPrompt, userPrompt, tools) {
  return Cg0({
    client: Q,
    tool: A,
    args: B,
    signal: new AbortController().signal,
    isNonInteractiveSession: I
  })
}
async function Zu1(messages, systemPrompt) {
  let Q  =  !1,
    I  =  KV(),
    G  =  B ? {
      ...I,
      ...B
    } : I;
  await Promise.all(Object.entries(G).map(async ([D, Z])  = > {
    let Y  =  await RY0(D, Z);
    if (Y.type !== "connected") {
      A({
        client: Y,
        tools: [],
        commands: []
      });
      return
    }
    let W  =  !!Y.capabilities?.resources,
      [F, J]  =  await Promise.all([DF6(Y), ZF6(Y)]),
      C  =  [];
    A({
      client: Y,
      tools: [...F, ...C],
      commands: J
    })
  }))
}
async function Fg0(messages) {
  return new Promise((B)  = > {
    let Q  =  0,
      I  =  0,
      G  =  KV(),
      D  =  A ? {
        ...G,
        ...A
      } : G;
    if (Q  =  Object.keys(D).length, Q === 0) {
      B({
        clients: [],
        tools: [],
        commands: []
      });
      return
    }
    let Z  =  [],
      Y  =  [],
      W  =  [];
    Zu1((F)  = > {
      if (Z.push(F.client), Y.push(...F.tools), W.push(...F.commands), I++, I >= Q) B({
        clients: Z,
        tools: Y,
        commands: W
      })
    }, A)
  })
}

function Jg0(messages, systemPrompt) {
  switch (A.type) {
    case "text":
      return [{
        type: "text",
        text: A.text
      }];
    case "image":
      return [{
        type: "image",
        source: {
          data: String(A.data),
          media_type: A.mimeType || "image/jpeg",
          type: "base64"
        }
      }];
    case "resource": {
      let Q  =  A.resource,
        I  =  "";
      if ("text" in Q) return [{
        type: "text",
        text: `${ Q.text }`
      }];
      else if ("blob" in Q)
        if (BF6.has(Q.mimeType ?? "")) {
          let D  =  [];
          return D.push({
            type: "image",
            source: {
              data: Q.blob,
              media_type: Q.mimeType || "image/jpeg",
              type: "base64"
            }
          }), D
        } else return [{
          type: "text",
          text: `Base64 data (${ Q.mimeType||"unknown type" }) ${ Q.blob }`
        }];
      return []
    }
    default:
      return []
  }
}
async function Cg0({
  client: {
    client: messages,
    name: B
  },
  tool: userPrompt,
  args: tools,
  signal: signal,
  isNonInteractiveSession: D
}) {
  try {
    let Z  =  await A.callTool({
      name: Q,
      arguments: I
    }, Eb, {
      signal: G,
      timeout: QF6
    });
    if ("isError" in Z && Z.isError) {
      let W  =  `Error calling tool ${ Q }: ${ Z.error }`;
      throw GY(B, W), Error(W)
    }
    if ("toolResult" in Z) {
      let F  =  await oX("claude_code_unicode_sanitize") ? Ff1(String(Z.toolResult)) : String(Z.toolResult);
      if (B !== "ide") await Du1(F, Q, D);
      return F
    }
    if ("content" in Z && Array.isArray(Z.content)) {
      let W  =  Z.content,
        C  =  (await oX("claude_code_unicode_sanitize") ? IL(W) : W).map((X)  = > Jg0(X, B)).flat();
      if (B !== "ide") await Du1(C, Q, D);
      return C
    }
    let Y  =  `Unexpected response format from tool ${ Q }`;
    throw GY(B, Y), Error(Y)
  } catch (Z) {
    if (Z instanceof Nr) throw Z;
    if (!(Z instanceof Error) || Z.name !== "AbortError") throw Z
  }
}
class xV {
  static instance;
  baseline  =  new Map;
  initialized  =  !1;
  mcpClient;
  lastProcessedTimestamps  =  new Map;
  lastDiagnosticsByUri  =  new Map;
  rightFileDiagnosticsState  =  new Map;
  static getInstance() {
    if (!xV.instance) xV.instance  =  new xV;
    return xV.instance
  }
  initialize(A) {
    if (this.initialized) return;
    if (this.mcpClient  =  A, this.initialized  =  !0, this.mcpClient && this.mcpClient.type === "connected") {
      let B  =  i.object({
        method: i.literal("diagnostics_changed"),
        params: i.object({
          uri: i.string()
        })
      });
      this.mcpClient.client.setNotificationHandler(B, async (Q)  = > {
        let {
          uri: I
        }  =  Q.params;
        this.handleDiagnosticChange(I)
      })
    }
  }
  async shutdown() {
    this.initialized  =  !1, this.baseline.clear()
  }
  reset() {
    this.baseline.clear(), this.rightFileDiagnosticsState.clear()
  }
  normalizeFileUri(A) {
    let B  =  ["file://", "_claude_fs_right:", "_claude_fs_left:"];
    for (let Q of B)
      if (A.startsWith(Q)) return A.slice(Q.length);
    return A
  }
  async ensureFileOpened(A) {
    if (!this.initialized || !this.mcpClient || this.mcpClient.type !== "connected") return;
    try {
      await tM("openFile", {
        filePath: A,
        preview: !1,
        startText: "",
        endText: "",
        selectToEndOfLine: !1,
        makeFrontmost: !1
      }, this.mcpClient, !1)
    } catch (B) {
      logError(B)  // 记录错误信息
    }
  }
  async beforeFileEdited(A) {
    if (!this.initialized || !this.mcpClient || this.mcpClient.type !== "connected") return;
    await this.ensureFileOpened(A);
    let B  =  Date.now();
    try {
      let Q  =  await tM("getDiagnostics", {
          uri: `file://${ A }`
        }, this.mcpClient, !1),
        I  =  this.parseDiagnosticResult(Q)[0];
      if (I) {
        if (A !== this.normalizeFileUri(I.uri)) {
          logError(new Error(`Diagnostics file path mismatch: expected ${ A }, got ${ I.uri })`));  // 记录错误信息
          return
        }
        this.baseline.set(A, I.diagnostics), this.lastProcessedTimestamps.set(A, B)
      } else this.baseline.set(A, []), this.lastProcessedTimestamps.set(A, B)
    } catch (Q) { }
  }
  async getNewDiagnostics() {
    if (!this.initialized || !this.mcpClient || this.mcpClient.type !== "connected") return [];
    let A  =  [];
    try {
      let G  =  await tM("getDiagnostics", { }, this.mcpClient, !1);
      A  =  this.parseDiagnosticResult(G)
    } catch (G) {
      return []
    }
    let B  =  A.filter((G)  = > this.baseline.has(this.normalizeFileUri(G.uri))).filter((G)  = > G.uri.startsWith(
        "file://")),
      Q  =  new Map;
    A.filter((G)  = > this.baseline.has(this.normalizeFileUri(G.uri))).filter((G)  = > G.uri.startsWith(
      "_claude_fs_right:")).forEach((G)  = > {
      Q.set(this.normalizeFileUri(G.uri), G)
    });
    let I  =  [];
    for (let G of B) {
      let D  =  this.normalizeFileUri(G.uri),
        Z  =  this.baseline.get(D) || [],
        Y  =  Q.get(D),
        W  =  G;
      if (Y) {
        let J  =  this.rightFileDiagnosticsState.get(D);
        if (!J || !this.areDiagnosticArraysEqual(J, Y.diagnostics)) W  =  Y;
        this.rightFileDiagnosticsState.set(D, Y.diagnostics)
      }
      let F  =  W.diagnostics.filter((J)  = > !Z.some((C)  = > this.areDiagnosticsEqual(J, C)));
      if (F.length > 0) I.push({
        uri: G.uri,
        diagnostics: F
      });
      this.baseline.set(D, W.diagnostics)
    }
    return I
  }
  parseDiagnosticResult(A) {
    if (Array.isArray(A)) {
      let B  =  A.find((Q)  = > Q.type === "text");
      if (B && "text" in B) return JSON.parse(B.text)
    }
    return []
  }
  areDiagnosticsEqual(A, B) {
    return A.message === B.message && A.severity === B.severity && A.source === B.source && A.code === B.code && A
      .range.start.line === B.range.start.line && A.range.start.character === B.range.start.character && A.range.end
      .line === B.range.end.line && A.range.end.character === B.range.end.character
  }
  areDiagnosticArraysEqual(A, B) {
    if (A.length !== B.length) return !1;
    return A.every((Q)  = > B.some((I)  = > this.areDiagnosticsEqual(Q, I))) && B.every((Q)  = > A.some((I)  = > this
      .areDiagnosticsEqual(I, Q)))
  }
  isLinterDiagnostic(A) {
    let B  =  ["eslint", "eslint-plugin", "tslint", "prettier", "stylelint", "jshint", "standardjs", "xo", "rome",
      "biome", "deno-lint", "rubocop", "pylint", "flake8", "black", "ruff", "clippy", "rustfmt", "golangci-lint",
      "gofmt", "swiftlint", "detekt", "ktlint", "checkstyle", "pmd", "sonarqube", "sonarjs"
    ];
    if (!A.source) return !1;
    let Q  =  A.source.toLowerCase();
    return B.some((I)  = > Q.includes(I))
  }
  handleDiagnosticChange(A) { }
  async handleQueryStart(A) {
    if (!this.initialized) {
      let B  =  A.find((Q)  = > Q.type === "connected" && Q.name === "ide");
      if (B) await this.initialize(B)
    } else this.reset()
  }
  static formatDiagnosticsSummary(A) {
    return A.map((B)  = > {
      let Q  =  B.uri.split("/").pop() || B.uri,
        I  =  B.diagnostics.map((G)  = > {
          return `  ${ xV.getSeveritySymbol(G.severity) } [Line ${ G.range.start.line+1 }:${ G.range.start.character+1 }] ${ G.message }${ G.code?` [${ G.code }]`:"" }${ G.source?` (${ G.source })`:"" }`
        }).join(`
`);
      return `${ Q }:
${ I }`
    }).join(`

`)
  }
  static getSeveritySymbol(A) {
    return {
      Error: H2.cross,
      Warning: H2.warning,
      Info: H2.info,
      Hint: H2.star
    } [A] || H2.bullet
  }
}
var fV  =  xV.getInstance();
var v8  =  J1(_1(), 1);
import * as Dp1 from "path";
import {
  extname as Xw6,
  relative as Vw6
} from "path";
var yh  =  J1(du1(), 1);
var XF1  =  J1(_1(), 1);

function CC({
  code: messages,
  language: B
}) {
  let Q  =  XF1.useMemo(()  = > {
    let I  =  Ex(A);
    try {
      if (yh.supportsLanguage(B)) return yh.highlight(I, {
        language: B
      });
      else return logError(new Error(`Language not supported while highlighting code, falling back to markdown: ${ B }`)),  // 记录错误信息
        yh.highlight(I, {
          language: "markdown"
        })
    } catch (G) {
      if (G instanceof Error && G.message.includes("Unknown language")) return logError(new Error(  // 记录错误信息
        `Language not supported while highlighting code, falling back to markdown: ${ G }`)), yh.highlight(I, {
        language: "markdown"
      })
    }
  }, [A, B]);
  return XF1.default.createElement(y, null, Q)
}
var kh  =  "NotebookRead",
  qm0  =  "Extract and read source code from all code cells in a Jupyter notebook.",
  Mm0  = 
  "Reads a Jupyter notebook (.ipynb file) and returns all of the cells with their outputs. Jupyter notebooks are interactive documents that combine code, text, and visualizations, commonly used for data analysis and scientific computing. The notebook_path parameter must be an absolute path, not a relative path.";
var oL  =  "Read",
  VK6  =  2000,
  KK6  =  2000,
  Lm0  =  "Read a file from the local filesystem.",
  Rm0  = 
  `Reads a file from the local filesystem. You can access any file directly by using this tool.
Assume this tool is able to read all files on the machine. If the User provides a path to a file assume that path is valid. It is okay to read a file that does not exist; an error will be returned.

Usage:
- The file_path parameter must be an absolute path, not a relative path
- By default, it reads up to ${ VK6 } lines starting from the beginning of the file
- You can optionally specify a line offset and limit (especially handy for long files), but it's recommended to read the whole file by not providing these parameters
- Any lines longer than ${ KK6 } characters will be truncated
- Results are returned using cat -n format, with line numbers starting at 1
- This tool allows ${ x0 } to read images (eg PNG, JPG, etc). When reading an image file the contents are presented visually as ${ x0 } is a multimodal LLM.
- For Jupyter notebooks (.ipynb files), use the ${ kh } instead
- You have the capability to call multiple tools in a single response. It is always better to speculatively read multiple files as a batch that are potentially useful. 
- You will regularly be asked to read screenshots. If the user provides a path to a screenshot ALWAYS use this tool to view the file at the path. This tool will work with all temporary file paths like /var/folders/123/abc/T/TemporaryItems/NSIRD_screencaptureui_ZfB1tD/Screenshot.png
- If you read a file that exists but has empty contents you will receive a system reminder warning in place of file contents.`;
import {
  isAbsolute as $K6,
  posix as bh,
  resolve as xm0,
  sep as qK6
} from "path";
var HK6  =  ["allow", "deny"];

function zK6(messages, systemPrompt) {
  if (!A || !A.permissions) return [];
  let {
    permissions: Q
  }  =  A, I  =  [];
  for (let G of HK6) {
    let D  =  Q[G];
    if (D)
      for (let Z of D) I.push({
        source: B,
        ruleBehavior: G,
        ruleValue: tL(Z)
      })
  }
  return I
}

function wK6(messages, systemPrompt) {
  if (!A.allowedTools || A.allowedTools.length < 1) return [];
  let Q  =  new Set;
  for (let G of B)
    if (G.ruleBehavior === "allow" && G.source === "localSettings") Q.add(s8(G.ruleValue));
  let I  =  new Set;
  for (let G of A.allowedTools)
    if (!Q.has(G)) I.add(G);
  return Array.from(I)
}

function uu1() {
  let A  =  E9(),
    B  =  wK6(A, Tm0("localSettings"));
  if (B.length < 1) return;
  pu1({
    ruleValues: B.map(tL),
    ruleBehavior: "allow"
  }, "localSettings")
}

function Om0() {
  let A  =  [],
    B  =  E9();
  for (let Q of B.allowedTools) A.push({
    source: "projectSettings",
    ruleBehavior: "allow",
    ruleValue: tL(Q)
  });
  for (let Q of fq) A.push(...Tm0(Q));
  return A
}

function Tm0(messages) {
  let B  =  vq(A);
  return zK6(B, A)
}

function Pm0(messages) {
  let B  =  s8(A.ruleValue),
    Q  =  vq(A.source);
  if (!Q || !Q.permissions) return !1;
  let I  =  Q.permissions[A.ruleBehavior];
  if (!I || !I.includes(B)) return !1;
  try {
    let G  =  {
      ...Q,
      permissions: {
        ...Q.permissions,
        [A.ruleBehavior]: I.filter((D)  = > D !== B)
      }
    };
    return SX(A.source, G), !0
  } catch (G) {
    return logError(G instanceof Error ? G : new Error(String(G))), !1  // 记录错误信息
  }
}

function EK6() {
  return {
    permissions: {
      allow: [],
      deny: []
    }
  }
}

function pu1({
  ruleValues: messages,
  ruleBehavior: B
}, userPrompt) {
  if (A.length < 1) return !0;
  let I  =  A.map(s8),
    G  =  vq(Q) || EK6();
  try {
    let D  =  G.permissions || { },
      Z  =  {
        ...G,
        permissions: {
          ...D,
          [B]: [...D[B] || [], ...I]
        }
      };
    return SX(Q, Z), !0
  } catch (D) {
    return logError(D instanceof Error ? D : new Error(String(D))), !1  // 记录错误信息
  }
}
var Sm0  =  [...fq, "cliArg"];

function _m0(messages) {
  switch (A) {
    case "cliArg":
      return "CLI argument";
    case "localSettings":
      return "project local settings";
    case "projectSettings":
      return "project settings";
    case "policySettings":
      return "policy settings";
    case "userSettings":
      return "user settings"
  }
}

function tL(messages) {
  let B  =  A.match(/^([^(]+)\(([^)]+)\)$/);
  if (!B) return {
    toolName: A
  };
  let Q  =  B[1],
    I  =  B[2];
  if (!Q || !I) return {
    toolName: A
  };
  return {
    toolName: Q,
    ruleContent: I
  }
}

function s8(messages) {
  return A.ruleContent ? `${ A.toolName }(${ A.ruleContent })` : A.toolName
}

function VF1(messages) {
  return Sm0.flatMap((B)  = > (A.alwaysAllowRules[B] || []).map((Q)  = > ({
    source: B,
    ruleBehavior: "allow",
    ruleValue: tL(Q)
  })))
}

function xh(messages) {
  return Sm0.flatMap((B)  = > (A.alwaysDenyRules[B] || []).map((Q)  = > ({
    source: B,
    ruleBehavior: "deny",
    ruleValue: tL(Q)
  })))
}

function UK6(messages, systemPrompt) {
  return VF1(A).find((Q)  = > Q.ruleValue.toolName === B.name && Q.ruleValue.ruleContent === void 0) || null
}

function NK6(messages, systemPrompt) {
  return xh(A).find((Q)  = > Q.ruleValue.toolName === B.name && Q.ruleValue.ruleContent === void 0) || null
}

function fh(messages, systemPrompt, userPrompt) {
  return cu1(A, B.name, Q)
}

function cu1(messages, systemPrompt, userPrompt) {
  let I  =  new Map,
    G  =  [];
  switch (Q) {
    case "allow":
      G  =  VF1(A);
      break;
    case "deny":
      G  =  xh(A);
      break
  }
  for (let D of G)
    if (D.ruleValue.toolName === B && D.ruleValue.ruleContent !== void 0 && D.ruleBehavior === Q) I.set(D.ruleValue
      .ruleContent, D);
  return I
}
var vh  =  async (A, B, Q)  = > {
  if (Q.abortController.signal.aborted) throw new CancellationError;  // 检查是否已取消操作
  let I  =  NK6(Q.getToolPermissionContext(), A);  // 获取工具权限上下文
  if (I) return {
    behavior: "deny",
    decisionReason: {
      type: "rule",
      rule: I
    },
    ruleSuggestions: null,
    message: `Permission to use ${ A.name } has been denied.`
  };
  let G  =  void 0;
  try {
    let Z  =  A.inputSchema.parse(B);
    G  =  await A.checkPermissions(Z, Q)
  } catch (Z) {
    return logError(Z), {  // 记录错误信息
      behavior: "ask",
      message: "Error checking permissions"
    }
  }
  if (G?.behavior === "deny") return G;
  if (Q.getToolPermissionContext().mode === "bypassPermissions") return {  // 获取工具权限上下文
    behavior: "allow",
    updatedInput: B,
    decisionReason: {
      type: "mode",
      mode: Q.getToolPermissionContext().mode  // 获取工具权限上下文
    }
  };
  let D  =  UK6(Q.getToolPermissionContext(), A);  // 获取工具权限上下文
  if (D) return {
    behavior: "allow",
    updatedInput: B,
    decisionReason: {
      type: "rule",
      rule: D
    }
  };
  if (G.behavior === "allow") return G;
  return {
    ...G,
    behavior: "ask",
    message: `Claude requested permissions to use ${ A.name }, but you haven't granted it yet.`
  }
};

function jm0(messages) {
  switch (A) {
    case "allow":
      return "alwaysAllowRules";
    case "deny":
      return "alwaysDenyRules"
  }
}
async function KF1(messages) {
  return xr({
    ...A,
    ruleValues: [A.rule.ruleValue],
    ruleBehavior: A.rule.ruleBehavior,
    destination: A.rule.source
  })
}
async function xr({
  ruleBehavior: messages,
  destination: systemPrompt,
  initialContext: userPrompt,
  setToolPermissionContext: tools,
  ruleValues: G
}) {
  let D  =  new Set(G.map(s8)),
    Z  =  jm0(A),
    Y  =  {
      ...Q,
      [Z]: {
        ...Q[Z],
        [B]: [...Q[Z][B] || [], ...D]
      }
    };
  if (B === "localSettings") uu1();
  pu1({
    ruleValues: G,
    ruleBehavior: A
  }, B), I(Y)
}
async function ym0({
  rule: messages,
  initialContext: systemPrompt,
  setToolPermissionContext: Q
}) {
  if (A.source === "policySettings") throw new Error("Cannot delete permission rules from managed settings");
  let I  =  s8(A.ruleValue),
    G  =  jm0(A.ruleBehavior),
    D  =  A.source,
    Z  =  {
      ...B,
      [G]: {
        ...B[G],
        [A.source]: B[G][D]?.filter((Y)  = > Y !== I) || []
      }
    };
  if (A.source === "localSettings") uu1();
  switch (D) {
    case "localSettings":
    case "userSettings":
    case "projectSettings": {
      Pm0(A);
      break
    }
    case "cliArg":
      break
  }
  Q(Z)
}

function km0(messages, systemPrompt) {
  let Q  =  {
      ...A.alwaysAllowRules
    },
    I  =  {
      ...A.alwaysDenyRules
    };
  for (let G of B) {
    let D  =  s8(G.ruleValue),
      Z  =  G.source,
      Y  =  (()  = > {
        switch (G.ruleBehavior) {
          case "allow":
            return Q;
          case "deny":
            return I
        }
      })();
    if (!Y[Z]) Y[Z]  =  [];
    if (Y[Z]) Y[Z].push(D)
  }
  return {
    ...A,
    alwaysAllowRules: Q,
    alwaysDenyRules: I
  }
}
var fm0  =  J1(t91(), 1);
import {
  homedir as MK6
} from "os";
var CN  =  bh.sep;

function LK6() {
  return fq.map((A)  = > _41(A))
}

function CG(messages) {
  return $K6(A) ? xm0(A) : xm0(uA(), A)
}

function SY(messages) {
  let B  =  CG(A),
    Q  =  CG(u4());
  if (!B.startsWith(Q)) return !1;
  let I  =  B[Q.length];
  if (I === void 0 || I === qK6) return !0;
  return !1
}

function RK6(messages) {
  switch (A) {
    case "cliArg":
      return CG(u4());
    case "userSettings":
    case "policySettings":
    case "projectSettings":
    case "localSettings":
      return S41(A)
  }
}

function lu1(messages) {
  return bh.join(CN, A)
}

function OK6({
  patternRoot: messages,
  pattern: systemPrompt,
  rootPath: Q
}) {
  let I  =  bh.join(A, B);
  if (A === Q) return lu1(B);
  else if (I.startsWith(`${ Q }${ CN }`)) {
    let G  =  I.slice(Q.length);
    return lu1(G)
  } else {
    let G  =  bh.relative(Q, A);
    if (!G || G.startsWith(`..${ CN }`) || G === "..") return null;
    else {
      let D  =  bh.join(G, B);
      return lu1(D)
    }
  }
}

function vm0(messages, systemPrompt) {
  let Q  =  new Set(A.get(null) ?? []);
  for (let [I, G] of A.entries()) {
    if (I === null) continue;
    for (let D of G) {
      let Z  =  OK6({
        patternRoot: I,
        pattern: D,
        rootPath: B
      });
      if (Z) Q.add(Z)
    }
  }
  return Array.from(Q)
}

function zF1(messages) {
  let B  =  gm0(A, "read", "deny"),
    Q  =  new Map;
  for (let [G, D] of B.entries()) Q.set(G, Array.from(D.keys()));
  let I  =  E9().ignorePatterns;
  if (I && I.length > 0)
    for (let G of I) {
      let {
        relativePattern: D,
        root: Z
      }  =  bm0(G, "projectSettings"), Y  =  Q.get(Z);
      if (Y === void 0) Y  =  [D], Q.set(Z, Y);
      else Y.push(D)
    }
  return Q
}

function bm0(messages, systemPrompt) {
  if (A.startsWith(`${ CN }${ CN }`)) return {
    relativePattern: A.slice(1),
    root: CN
  };
  else if (A.startsWith(`~${ CN }`)) return {
    relativePattern: A.slice(1),
    root: MK6()
  };
  else if (A.startsWith(CN)) return {
    relativePattern: A,
    root: RK6(B)
  };
  return {
    relativePattern: A,
    root: null
  }
}

function gm0(messages, systemPrompt, userPrompt) {
  let I  =  (()  = > {
      switch (B) {
        case "edit":
          return _U;
        case "read":
          return oL
      }
    })(),
    G  =  cu1(A, I, Q),
    D  =  new Map;
  for (let [Z, Y] of G.entries()) {
    let {
      relativePattern: W,
      root: F
    }  =  bm0(Z, Y.source), J  =  D.get(F);
    if (J === void 0) J  =  new Map, D.set(F, J);
    J.set(W, Y)
  }
  return D
}

function HF1(messages, systemPrompt, userPrompt, tools) {
  let G  =  CG(A),
    D  =  gm0(B, Q, I);
  for (let [Z, Y] of D.entries()) {
    let W  =  fm0.default().add(Array.from(Y.keys())),
      F  =  bh.relative(Z ?? uA(), G);
    if (F.startsWith(`..${ CN }`)) continue;
    let J  =  W.test(F);
    if (J.ignored && J.rule) return Y.get(J.rule.pattern) ?? null
  }
  return null
}

function lz(messages, systemPrompt, userPrompt) {
  if (typeof A.getPath !== "function") return {
    behavior: "ask",
    message: `Claude requested permissions to use ${ A.name }, but you haven't granted it yet.`
  };
  let I  =  A.getPath(B),
    G  =  B_(A, B, Q);
  if (G.behavior === "allow") return G;
  let D  =  HF1(I, Q, "read", "deny");
  if (D) return {
    behavior: "deny",
    message: `Permission to read ${ I } has been denied.`,
    decisionReason: {
      type: "rule",
      rule: D
    },
    ruleSuggestions: null
  };
  if (SY(I)) return {
    behavior: "allow",